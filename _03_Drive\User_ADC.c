/* ****************************
 * Project description:
 *
 * ADC initialization file
 *
 * Author: 创新基地 -> 2019 Mao
 *
 * Creation Date: 2021/10/06 - 1
 * ****************************/
 
 /* ***************************** Include & Define Part     	*****************************/
#include "User_ADC.h"
 
#define BaseVol 3.299f
 
uint8_t ADC_Sign = 0;
uint32_t ADCData[ADCDataLength];

arm_biquad_casd_df1_inst_f32 iir_instance;
float32_t iir_state[4] = {0}; // 存储x[n-1], x[n-2], y[n-1], y[n-2]
float32_t iir_coeff[5]; // b0, b1, b2, -a1, -a2

// IIR滤波器系数(可配置常量)
static volatile float iir_b0 = 1.0f;  // 分子系数 z^0
static volatile float iir_b1 = 0.0f;  // 分子系数 z^(-1)  
static volatile float iir_b2 = 0.0f;  // 分子系数 z^(-2)
static volatile float iir_a1 = 0.0f;  // 分母系数 z^(-1)
static volatile float iir_a2 = 0.0f;  // 分母系数 z^(-2)

// 历史数据缓冲区(内存对齐优化)
__attribute__((aligned(8))) static float iir_x_hist[2] = {0.0f};  // x[n-1], x[n-2]
__attribute__((aligned(8))) static float iir_y_hist[2] = {0.0f};  // y[n-1], y[n-2]

// 滤波器控制标志
static volatile uint8_t iir_filter_enabled = 0;  // 0=关闭, 1=启用

// 性能监控变量
static volatile uint32_t iir_process_count = 0;  // 处理样本计数器

/* ***************************** Initialization Part        *****************************
 * 初始化函数区
 * */

// 初始化ADC 参数：模式序号
// 注：所有模式均由TIM3触发采样，即都被配置为单次采样
// mode -> 0 ：独立模式单通道采样
// mode -> 1 ：双通道规则同步采样
void User_ADC_Init( uint8_t mode )
{
	if( mode == 0 )									// 独立模式单通道采样
	{
		User_ADC_GPIO_Init(1);	
		
		ADC_Mode_Independent_Init();
		
		//ADC_DMA_Init( 0 );
		IIR_ADC_DMA_Init( 0 );
		
		ADC_DMA_NVIC_Init();		
		
		ADC_TIM3_Init( 5000 );	// 初始化采样率为5kHz
	}
	else if( mode == 1 )						// 双通道规则同步采样
	{
		User_ADC_GPIO_Init(2);
		
		ADC_DualMode_RegSimult_Init();
		
		//ADC_DMA_Init( 1 );
		IIR_ADC_DMA_Init( 0 );
		
		ADC_DMA_NVIC_Init();
		
		ADC_TIM3_Init( 5000 );	// 初始化采样率为5kHz
	}
}
 
// 采样GPIO口初始化 参数：采样通道数量
void User_ADC_GPIO_Init( uint8_t ch_num )	
{
	GPIO_InitTypeDef GPIO_InitStruct;
	
	// Configure ADC -> GPIO
	RCC_AHB1PeriphClockCmd( RCC_AHB1Periph_GPIOA , ENABLE );
	
	if( ch_num == 1 )
		GPIO_InitStruct.GPIO_Pin = GPIO_Pin_1;
	else
		GPIO_InitStruct.GPIO_Pin = GPIO_Pin_1 | GPIO_Pin_2;
	
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AN;
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
	GPIO_InitStruct.GPIO_Speed = GPIO_High_Speed;
	
	GPIO_Init( GPIOA , &GPIO_InitStruct );
}

// 独立模式单通道单次转换初始化(ADC1)
void ADC_Mode_Independent_Init()
{
	// 创建 ADC初始化结构体 以及 ADC共享初始化结构体
	ADC_InitTypeDef ADC_InitStruct;
	ADC_CommonInitTypeDef ADC_ComInitStruct;
	
	// 使能ADC1时钟
	RCC_APB2PeriphClockCmd( RCC_APB2Periph_ADC1 , ENABLE );	
	
	// 配置所有ADC工作模式
	ADC_ComInitStruct.ADC_Mode = ADC_Mode_Independent;													// 工作模式 -> 独立模式
	ADC_ComInitStruct.ADC_Prescaler = ADC_Prescaler_Div2;												// 分频系数 -> 四分频
	ADC_ComInitStruct.ADC_DMAAccessMode = ADC_DMAAccessMode_1;									// DMA传输模式 -> 模式1
	ADC_ComInitStruct.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_20Cycles;			// 采样间隔 -> 20个时钟周期
	ADC_CommonInit( &ADC_ComInitStruct );
	
	// 配置ADC1工作方式
	ADC_InitStruct.ADC_Resolution            = ADC_Resolution_12b;            	// 采样分辨率 -> 12位  
	ADC_InitStruct.ADC_ScanConvMode          = DISABLE;													// 扫描转换 -> 失能
	ADC_InitStruct.ADC_ContinuousConvMode    = DISABLE;													// 连续转换 -> 失能
	ADC_InitStruct.ADC_ExternalTrigConvEdge  = ADC_ExternalTrigConvEdge_Rising;	// 外部触发转换边沿 -> 上升沿
	ADC_InitStruct.ADC_ExternalTrigConv      = ADC_ExternalTrigConv_T3_TRGO;		// 外部触发转换 -> TIM3触发
	ADC_InitStruct.ADC_DataAlign             = ADC_DataAlign_Right;							// 数据对齐方式 -> 右对齐
	ADC_InitStruct.ADC_NbrOfConversion       = 1;    														// 转换数量 -> 1个
	ADC_Init( ADC1 , &ADC_InitStruct );
	
	// 配置ADC1规则通道组
	//ADC_RegularChannelConfig( ADC1 , ADC_Channel_1 , 1 , ADC_SampleTime_15Cycles );
	ADC_RegularChannelConfig( ADC1 , ADC_Channel_1 , 1 , ADC_SampleTime_3Cycles );
	
	// 允许在最后一次ADC转换后发出DMA请求
	ADC_DMARequestAfterLastTransferCmd( ADC1 , ENABLE );
	
	// 允许ADC1使用DMA
	ADC_DMACmd( ADC1 , ENABLE );
	
	// 使能ADC1
	ADC_Cmd( ADC1 , ENABLE );
}

// 双通道ADC规则采样初始化
void ADC_DualMode_RegSimult_Init()
{
	// Creat ADC InitStruct and ComInitStruct
	ADC_InitTypeDef ADC_InitStruct;
	ADC_CommonInitTypeDef ADC_ComInitStruct;
	
	// Enable the ADC clock
	RCC_APB2PeriphClockCmd( RCC_APB2Periph_ADC1 , ENABLE );
	RCC_APB2PeriphClockCmd( RCC_APB2Periph_ADC2 , ENABLE );
	
	// Configure ADC Common
	ADC_ComInitStruct.ADC_Mode = ADC_DualMode_RegSimult;										// 工作模式 -> 双通道规则同步
	ADC_ComInitStruct.ADC_Prescaler = ADC_Prescaler_Div4;										// 分频系数 -> 四分频
	ADC_ComInitStruct.ADC_DMAAccessMode = ADC_DMAAccessMode_2;							// DMA传输模式 -> 模式2
	ADC_ComInitStruct.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;	// 采样间隔 -> 五个时钟周期
	
	ADC_CommonInit( &ADC_ComInitStruct );
	
	// Configure ADC
	ADC_InitStruct.ADC_Resolution            = ADC_Resolution_12b;       				// 采样分辨率 -> 12位  
	ADC_InitStruct.ADC_ScanConvMode          = ENABLE;													// 扫描转换 -> 使能
	ADC_InitStruct.ADC_ContinuousConvMode    = DISABLE;													// 连续转换 -> 失能
	ADC_InitStruct.ADC_ExternalTrigConvEdge  = ADC_ExternalTrigConvEdge_Rising; // 外部触发转换边沿 -> 上升沿
	ADC_InitStruct.ADC_ExternalTrigConv      = ADC_ExternalTrigConv_T3_TRGO;		// 外4 -> TIM3触发
	ADC_InitStruct.ADC_DataAlign             = ADC_DataAlign_Right;							// 数据对齐方式 -> 右对齐
	ADC_InitStruct.ADC_NbrOfConversion       = 1;    														// 转换数量 -> 1个
	
	ADC_Init( ADC1 , &ADC_InitStruct );	
	ADC_Init( ADC2 , &ADC_InitStruct );
	
	// Configure ADC Channel
	ADC_RegularChannelConfig( ADC1 , ADC_Channel_1 , 1 , ADC_SampleTime_15Cycles );
	ADC_RegularChannelConfig( ADC2 , ADC_Channel_2 , 1 , ADC_SampleTime_15Cycles );
	
	ADC_DMARequestAfterLastTransferCmd( ADC1 , ENABLE );
	ADC_DMARequestAfterLastTransferCmd( ADC2 , ENABLE );
	
	// Enable ADC -> DMA
	ADC_DMACmd( ADC1 , ENABLE );
	ADC_DMACmd( ADC2 , ENABLE );
	
	// Enable ADC
	ADC_Cmd( ADC1 , ENABLE );
	ADC_Cmd( ADC2 , ENABLE );
}

// ADC采样传输DMA初始化 参数：1>工作模式
void ADC_DMA_Init( uint8_t mode )	
{
	DMA_InitTypeDef DMA_InitStruct;
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
	
	DMA_DeInit( DMA2_Stream0 );																						// 初始化数据流
	
	if( mode == 0 )
		DMA_InitStruct.DMA_PeripheralBaseAddr = (uint32_t)&ADC1 -> DR;			// 源地址 -> ADC1的规则数据寄存器
	else
		DMA_InitStruct.DMA_PeripheralBaseAddr = (uint32_t)&ADC -> CDR;			// 源地址 -> ADC通用规则数据寄存器
	
	DMA_InitStruct.DMA_Channel = DMA_Channel_0;														// DMA通道
	DMA_InitStruct.DMA_Memory0BaseAddr = (uint32_t)&ADCData;							// 目标地址
	DMA_InitStruct.DMA_DIR = DMA_DIR_PeripheralToMemory;									// 传输方向(外设->内存)
	DMA_InitStruct.DMA_BufferSize = ADCDataLength;												// 数据大小
  DMA_InitStruct.DMA_PeripheralInc = DMA_PeripheralInc_Disable;					// 源地址是否递增
  DMA_InitStruct.DMA_MemoryInc = DMA_MemoryInc_Enable;									// 目标地址是否递增
  DMA_InitStruct.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Word;	// 源数据大小(字节)
  DMA_InitStruct.DMA_MemoryDataSize = DMA_MemoryDataSize_Word;					// 目标数据大小(字节)
  DMA_InitStruct.DMA_Mode = DMA_Mode_Circular;													// 工作模式
  DMA_InitStruct.DMA_Priority = DMA_Priority_High;
  DMA_InitStruct.DMA_FIFOMode = DMA_FIFOMode_Disable;         
  DMA_InitStruct.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;
  DMA_InitStruct.DMA_MemoryBurst = DMA_MemoryBurst_Single;
  DMA_InitStruct.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
  DMA_Init( DMA2_Stream0 , &DMA_InitStruct );
	
	// 清除DMA2中断标志
	DMA_ClearFlag( DMA2_Stream0 , DMA_FLAG_TCIF0 );  
	// 使能传输完成时触发中断
	DMA_ITConfig( DMA2_Stream0 , DMA_IT_TC , ENABLE );  
	
	// 使能DMA2
	DMA_Cmd(DMA2_Stream0, ENABLE); 
}

// 采样传输DMA的中断初始化
void ADC_DMA_NVIC_Init()
{
	NVIC_InitTypeDef NVIC_InitStruct;
	
	// Configure DMA -> NVIC
	NVIC_InitStruct.NVIC_IRQChannel = DMA2_Stream0_IRQn;
	NVIC_InitStruct.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStruct.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStruct.NVIC_IRQChannelSubPriority = 0;
	
	NVIC_PriorityGroupConfig( NVIC_PriorityGroup_2 );
	NVIC_Init( &NVIC_InitStruct );
}

// ADC采样定时器TIM3初始化 参数：初始化采样率
void ADC_TIM3_Init( float fs )
{
	TIM_TimeBaseInitTypeDef TIM_BaseInitStruct;
	
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);
	
	TIM_BaseInitStruct.TIM_Prescaler = (1000 - 1);
	TIM_BaseInitStruct.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_BaseInitStruct.TIM_Period = (21 - 1);
	TIM_BaseInitStruct.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_BaseInitStruct.TIM_RepetitionCounter = 0;
	
	TIM_TimeBaseInit( TIM3 , &TIM_BaseInitStruct );
	
	TIM_SelectOutputTrigger( TIM3 , TIM_TRGOSource_Update );
	
	TIM_UpdateDisableConfig( TIM3 , DISABLE ); //?
	
	//TIM_DMACmd( TIM3 , TIM_DMA_Update , ENABLE );
	
	TIM_Cmd( TIM3 , DISABLE );
	
	Set_SamplingFre( fs );
}

/* ***************************** Custom Function Part       *****************************
 * 自定义函数区
 */

// 获取电压平均值(用于直流直接采集) 参数：两个ADC转换的电压的平均值(大小为2的数组)(输入指针)
void Get_DCVol( float vol[2] )
{
	float temp_vol1 , temp_vol2;
	uint32_t count;
	
	ADCData[ADCDataLength - 1] = 0;
	TIM_Cmd( TIM3 , ENABLE );
	while( ADCData[ADCDataLength - 1] == 0 );
	TIM_Cmd( TIM3 , DISABLE );
	
	vol[0] = vol[1] = 0;
	for( count = 0 ; count < ADCDataLength ; count ++ )
	{
		temp_vol1 = (float)( ADCData[count] & 0x0000ffff ) / 0x0fff * BaseVol;
		temp_vol2 = (float)( ADCData[count] >> 16 ) / 0xfff * BaseVol;
		vol[0] = vol[0] + temp_vol1;
		vol[1] = vol[1] + temp_vol2; 
	}
	vol[0] = vol[0] / ADCDataLength;
	vol[1] = vol[1] / ADCDataLength;
}

// 将所有AD数据转换为电压(用于采集交流电压) 参数：1>ADC1转换的电压数据；2>ADC2转换的电压数据(输入两指针)
void Get_ACVol( float vol1_data[ADCDataLength] , float vol2_data[ADCDataLength] )
{
	uint32_t count;	//计数器
	
	// 标志位清零
	ADC_Sign = 0;
	// 使能TIM3，开始采样
	TIM_Cmd( TIM3 , ENABLE );
	// 标志位为1则采样结束
	while( ADC_Sign == 0 );
	// 关闭定时器( 后改为了使用DMA中断关闭 )
	//TIM_Cmd( TIM3 , DISABLE );
	

	// 将ADC数据转换为电压返回
	for( count = 0 ; count < ADCDataLength ; count ++ )
	{
		// 低16位为ADC1的数据
		vol1_data[count] = (float)( ADCData[count] & 0x0000ffff ) / 0xfff * BaseVol;
		// 高16位为ADC2的数据
		vol2_data[count] = (float)( ADCData[count] >> 16 ) / 0xfff * BaseVol;
	}
	for(count=0;count<1024;count++)
	{
		printf("%d,%d\n",vol1_data[count],vol2_data[count]);
	}
}

// 设置TIM3采样率 参数：1>预设采样率 返回：实际采样率
float Set_SamplingFre( float fs )
{
	uint32_t psc = 1;
	uint32_t arr = 1;
	
	TIM_Cmd(TIM3 , DISABLE);						// 关闭定时器
	
	do																	// 循环查找满足条件的PSC和ARR值
	{
		arr = 84000000 / (fs * psc);
		psc ++;
	}
	while( arr > 65535 );
	psc --;
	
	fs = (float)84000000 / (psc * arr); // 由PSC和ARR算出实际采样率
	
	TIM3 -> ARR = arr - 1;							// 改变定时器重装载值改变采样率
	TIM3 -> PSC = psc - 1;							// 改变定时器分频系数改变采样率
	
	return fs;
}

void IIR_ADC_DMA_Init( uint8_t mode )	
{
	DMA_InitTypeDef DMA_InitStruct;
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
	
	DMA_DeInit( DMA2_Stream0 );																						// 初始化数据流
	
	if( mode == 0 )
		DMA_InitStruct.DMA_PeripheralBaseAddr = (uint32_t)&ADC1 -> DR;			// 源地址 -> ADC1的规则数据寄存器
	else
		DMA_InitStruct.DMA_PeripheralBaseAddr = (uint32_t)&ADC -> CDR;			// 源地址 -> ADC通用规则数据寄存器
	
	DMA_InitStruct.DMA_Channel = DMA_Channel_0;														// DMA通道
	DMA_InitStruct.DMA_Memory0BaseAddr = (uint32_t)&ADCData;							// 目标地址
	DMA_InitStruct.DMA_DIR = DMA_DIR_PeripheralToMemory;									// 传输方向(外设->内存)
	DMA_InitStruct.DMA_BufferSize = 1;												// 数据大小
  DMA_InitStruct.DMA_PeripheralInc = DMA_PeripheralInc_Disable;					// 源地址是否递增
  DMA_InitStruct.DMA_MemoryInc = DMA_MemoryInc_Enable;									// 目标地址是否递增
  DMA_InitStruct.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Word;	// 源数据大小(字节)
  DMA_InitStruct.DMA_MemoryDataSize = DMA_MemoryDataSize_Word;					// 目标数据大小(字节)
  DMA_InitStruct.DMA_Mode = DMA_Mode_Circular;													// 工作模式
  DMA_InitStruct.DMA_Priority = DMA_Priority_High;
  DMA_InitStruct.DMA_FIFOMode = DMA_FIFOMode_Disable;         
  DMA_InitStruct.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;
  DMA_InitStruct.DMA_MemoryBurst = DMA_MemoryBurst_Single;
  DMA_InitStruct.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
  DMA_Init( DMA2_Stream0 , &DMA_InitStruct );
	
	// 清除DMA2中断标志
	DMA_ClearFlag( DMA2_Stream0 , DMA_FLAG_TCIF0 );  
	// 使能传输完成时触发中断
	DMA_ITConfig( DMA2_Stream0 , DMA_IT_TC , ENABLE );  
	
	// 使能DMA2
	DMA_Cmd(DMA2_Stream0, ENABLE); 
}

 
void IIR_Filter_Config(float b0, float b1, float b2, float a1, float a2) 
{
    __disable_irq();
    // 分子系数 (前向路径)
    iir_b0 = b0;  // z^0 系数
    iir_b1 = b1;  // z^(-1) 系数  
    iir_b2 = b2;  // z^(-2) 系数
    
    // 分母系数 (反馈路径，注意符号)
    iir_a1 = a1;  // z^(-1) 系数
    iir_a2 = a2;  // z^(-2) 系数
    __enable_irq();
}

void IIR_Filter_Reset(void) {
    __disable_irq();
    // 输入历史值：x[n-1] = 0, x[n-2] = 0
    iir_x_hist[0] = 0.0f;  // x[n-1]
    iir_x_hist[1] = 0.0f;  // x[n-2]
    
    // 输出历史值：y[n-1] = 0, y[n-2] = 0  
    iir_y_hist[0] = 0.0f;  // y[n-1]
    iir_y_hist[1] = 0.0f;  // y[n-2]
    
    iir_process_count = 0;
    __enable_irq();
}

void init_iir_filter(void) 
{
    iir_coeff[0] = iir_b0;
    iir_coeff[1] = iir_b1;
    iir_coeff[2] = iir_b2;
    iir_coeff[3] = -iir_a1;
    iir_coeff[4] = -iir_a2;
    arm_biquad_cascade_df1_init_f32(&iir_instance, 1, iir_coeff, iir_state);
}

void IIR_Filter_Enable(uint8_t enable) {
    iir_filter_enabled = enable;
    // enable = 1: 启动IIR滤波模式
    // enable = 0: 关闭滤波，恢复原有的简单中断处理
}

void IIR_Init()
{
	User_ADC_Init(0);
	IIR_Filter_Reset();
	//IIR_Filter_Config(0.0198,0.0396,0.0198,-1.5645,0.6437);
	//IIR_Filter_Config(1,0,0,0,0);
	//IIR_Filter_Config(0.0009,0.0019,0.0009,-1.9112,0.9150);//低通
	//IIR_Filter_Config(0.9566,-1.9131,0.9566,-1.9112,0.9150);//高通
	//IIR_Filter_Config(0.0044,0.0000,-0.0044,-1.9911,0.9912);//带通
	//IIR_Filter_Config(0.9956,-1.9911,0.9956,-1.9911,0.9912);//带通
	init_iir_filter();
	IIR_Filter_Enable(1);

}

// 启动ADC连续采样
void Start_ADC_Sampling(void)
{
    ADC_Sign = 0;  // 清除标志位
    TIM_Cmd(TIM3, ENABLE);  // 启动定时器触发ADC
}

// 停止ADC采样
void Stop_ADC_Sampling(void)
{
    TIM_Cmd(TIM3, DISABLE);  // 停止定时器
}


//配置PC1为外部中断输入
void PC1_GPIO_Config(void) 
{
    GPIO_InitTypeDef GPIO_InitStruct;
    
    // 1. 使能GPIOC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
    
    // 2. 配置PC1为输入模式
    GPIO_InitStruct.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_UP;    // 内部上拉（按键按下为低电平）
    GPIO_InitStruct.GPIO_Speed = GPIO_Speed_2MHz; // 低速即可
    GPIO_Init(GPIOC, &GPIO_InitStruct);
}


////配置EXTI1外部中断线
void PC1_EXTI_Config(void) 
{
    EXTI_InitTypeDef EXTI_InitStruct;
    
    // 1. 使能SYSCFG时钟（用于EXTI配置）
     RCC_APB2PeriphClockCmd(RCC_APB2Periph_SYSCFG, ENABLE);
    
    // 2. 连接PC1到EXTI1线
     SYSCFG_EXTILineConfig(EXTI_PortSourceGPIOC, EXTI_PinSource1);
    
    // 3. 配置EXTI1
    EXTI_InitStruct.EXTI_Line = EXTI_Line1;
    EXTI_InitStruct.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStruct.EXTI_Trigger = EXTI_Trigger_Falling;  // 下降沿触发（按键按下）
    EXTI_InitStruct.EXTI_LineCmd = ENABLE;
    EXTI_Init(&EXTI_InitStruct);
}


// PC1设置为最高优先级
void PC1_NVIC_Config(void) 
{
    NVIC_InitTypeDef NVIC_InitStruct;
    
    NVIC_InitStruct.NVIC_IRQChannel = EXTI1_IRQn;
    NVIC_InitStruct.NVIC_IRQChannelPreemptionPriority = 0;  // 最高抢占优先级
    NVIC_InitStruct.NVIC_IRQChannelSubPriority = 0;        // 最高子优先级
    NVIC_InitStruct.NVIC_IRQChannelCmd = ENABLE;
    
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    NVIC_Init(&NVIC_InitStruct);
}

void PC1_Init(void) 
{
    PC1_GPIO_Config();
    PC1_EXTI_Config();
    PC1_NVIC_Config();
}


/* ***************************** IRQHandler Part    	     	*****************************
 * 中断执行函数区
 */


void EXTI1_IRQHandler(void) {
    // 检查中断标志
    if(EXTI_GetITStatus(EXTI_Line1) != RESET) {
        
        // 设置停止标志
        iir_filter_enabled = 0;
        
        // 清除中断标志
        EXTI_ClearITPendingBit(EXTI_Line1);
			//MenuHaddler_1();
    }
    // 总执行时间目标：<3μs
}


uint8_t first_flag = 1;
float max_value;
float min_value;
uint8_t change_flag;
float direct_val;
void DMA2_Stream0_IRQHandler()
{
	// 清除DMA标志
   DMA_ClearFlag(DMA2_Stream0, DMA_FLAG_TCIF0);
   
   if(!iir_filter_enabled) 
	{
       TIM_Cmd(TIM3, DISABLE);
       ADC_Sign = 1;
       return;
   }
   
   // 1. 读取ADC样本并转换
   float32_t xn = (float32_t)(ADCData[0] & 0x0000FFFF) * 0.0008056f;

   // 2. 直流分量检测(使用简化逻辑)
   if(first_flag) {
       max_value = min_value = xn;
       first_flag = 0;
   } else {
       // 使用宏来避免分支
       #define UPDATE_MAX(x, max, flag) do { \
           if((x) > (max)) { (max) = (x); (flag) = 1; } \
       } while(0)
       
       #define UPDATE_MIN(x, min, flag) do { \
           if((x) < (min)) { (min) = (x); (flag) = 1; } \
       } while(0)
       
       UPDATE_MAX(xn, max_value, change_flag);
       UPDATE_MIN(xn, min_value, change_flag);
   }
   
   // 计算直流值
   if(change_flag) {
       direct_val = (max_value + min_value) * 0.5f; // 乘法比除法快
       change_flag = 0;
   }
   
   // 3. 使用DSP库计算IIR滤波
   float32_t yn_filtered;
   arm_biquad_cascade_df1_f32(&iir_instance, &xn, &yn_filtered, 1);
   
   // 4. 添加直流分量
   float32_t yn = yn_filtered + direct_val*direct_coef;
   
   // 5. 限幅
   yn = (yn < 0.0f) ? 0.0f : ((yn > 3.3f) ? 3.3f : yn);
   
   // 6. 输出到DAC (预计算DAC比例因子)
   #define DAC_SCALE 1241.21f
   DAC->DHR12R1 = (uint32_t)(yn * DAC_SCALE);
   
   // 7. 设置标志
   ADC_Sign = 1;
}




irr_data sample_data;



 
 
/* ***************************** 						END 	   	     	*****************************/





