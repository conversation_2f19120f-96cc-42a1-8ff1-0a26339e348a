Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    app_touch.o(i.TouchStart_Judge) refers to app_touch.o(.bss) for TouchStruct
    app_touch.o(i.Touch_Clear) refers to app_touch.o(.bss) for TouchStruct
    app_touch.o(i.Touch_Judge) refers to app_touch.o(i.Touch_Clear) for Touch_Clear
    app_touch.o(i.Touch_Judge) refers to app_touch.o(.bss) for TouchStruct
    app_touch.o(i.Touch_main) refers to drive_touch.o(i.TouchRead) for TouchRead
    app_touch.o(i.Touch_main) refers to os_cpu.o(i.OSTimeDly) for OSTimeDly
    app_touch.o(i.Touch_main) refers to app_touch.o(.bss) for TouchStruct
    app_touch.o(i.Touch_main) refers to app_touch.o(.data) for LastX
    app_led.o(i.LED_main) refers to app_led.o(i.LED_Control) for LED_Control
    app_led.o(i.LED_main) refers to os_cpu.o(i.OSTimeDly) for OSTimeDly
    app_led.o(i.LED_main) refers to app_led.o(.data) for _led_cnt
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    user_adc.o(i.ADC_DMA_Init) refers to user_adc.o(.bss) for ADCData
    user_adc.o(i.ADC_DMA_NVIC_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    user_adc.o(i.ADC_DMA_NVIC_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) for TIM_UpdateDisableConfig
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.ADC_TIM3_Init) refers to user_adc.o(i.Set_SamplingFre) for Set_SamplingFre
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to arm_biquad_cascade_df1_f32.o(.text) for arm_biquad_cascade_df1_f32
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to user_adc.o(.data) for iir_filter_enabled
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to user_adc.o(.bss) for ADCData
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to user.o(.data) for direct_coef
    user_adc.o(i.EXTI1_IRQHandler) refers to stm32f4xx_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    user_adc.o(i.EXTI1_IRQHandler) refers to stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    user_adc.o(i.EXTI1_IRQHandler) refers to user_adc.o(.data) for iir_filter_enabled
    user_adc.o(i.Get_ACVol) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    user_adc.o(i.Get_ACVol) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    user_adc.o(i.Get_ACVol) refers to _printf_dec.o(.text) for _printf_int_dec
    user_adc.o(i.Get_ACVol) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.Get_ACVol) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_adc.o(i.Get_ACVol) refers to noretval__2printf.o(.text) for __2printf
    user_adc.o(i.Get_ACVol) refers to user_adc.o(.data) for ADC_Sign
    user_adc.o(i.Get_ACVol) refers to user_adc.o(.bss) for ADCData
    user_adc.o(i.Get_DCVol) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.Get_DCVol) refers to user_adc.o(.bss) for ADCData
    user_adc.o(i.IIR_ADC_DMA_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_adc.o(i.IIR_ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    user_adc.o(i.IIR_ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    user_adc.o(i.IIR_ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    user_adc.o(i.IIR_ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    user_adc.o(i.IIR_ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    user_adc.o(i.IIR_ADC_DMA_Init) refers to user_adc.o(.bss) for ADCData
    user_adc.o(i.IIR_Filter_Config) refers to user_adc.o(.data) for iir_b0
    user_adc.o(i.IIR_Filter_Enable) refers to user_adc.o(.data) for iir_filter_enabled
    user_adc.o(i.IIR_Filter_Reset) refers to user_adc.o(.data) for iir_x_hist
    user_adc.o(i.IIR_Init) refers to user_adc.o(i.User_ADC_Init) for User_ADC_Init
    user_adc.o(i.IIR_Init) refers to user_adc.o(i.IIR_Filter_Reset) for IIR_Filter_Reset
    user_adc.o(i.IIR_Init) refers to user_adc.o(i.init_iir_filter) for init_iir_filter
    user_adc.o(i.IIR_Init) refers to user_adc.o(i.IIR_Filter_Enable) for IIR_Filter_Enable
    user_adc.o(i.PC1_EXTI_Config) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_adc.o(i.PC1_EXTI_Config) refers to stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) for SYSCFG_EXTILineConfig
    user_adc.o(i.PC1_EXTI_Config) refers to stm32f4xx_exti.o(i.EXTI_Init) for EXTI_Init
    user_adc.o(i.PC1_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_adc.o(i.PC1_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_adc.o(i.PC1_Init) refers to user_adc.o(i.PC1_GPIO_Config) for PC1_GPIO_Config
    user_adc.o(i.PC1_Init) refers to user_adc.o(i.PC1_EXTI_Config) for PC1_EXTI_Config
    user_adc.o(i.PC1_Init) refers to user_adc.o(i.PC1_NVIC_Config) for PC1_NVIC_Config
    user_adc.o(i.PC1_NVIC_Config) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    user_adc.o(i.PC1_NVIC_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    user_adc.o(i.Set_SamplingFre) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.Start_ADC_Sampling) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.Start_ADC_Sampling) refers to user_adc.o(.data) for ADC_Sign
    user_adc.o(i.Stop_ADC_Sampling) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.User_ADC_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_adc.o(i.User_ADC_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.User_ADC_GPIO_Init) for User_ADC_GPIO_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_Mode_Independent_Init) for ADC_Mode_Independent_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.IIR_ADC_DMA_Init) for IIR_ADC_DMA_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_DMA_NVIC_Init) for ADC_DMA_NVIC_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_TIM3_Init) for ADC_TIM3_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_DualMode_RegSimult_Init) for ADC_DualMode_RegSimult_Init
    user_adc.o(i.init_iir_filter) refers to arm_biquad_cascade_df1_init_f32.o(.text) for arm_biquad_cascade_df1_init_f32
    user_adc.o(i.init_iir_filter) refers to user_adc.o(.data) for iir_b0
    user_adc.o(i.init_iir_filter) refers to user_adc.o(.bss) for iir_coeff
    user.o(i.AD9959_senddata) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    user.o(i.AD9959_senddata) refers to drive_communication.o(i.sendData) for sendData
    user.o(i.AD9959_senddata) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.AD9959_senddata) refers to drive_communication.o(.bss) for dds
    user.o(i.ADS1256_ReadVol) refers to drive_ads1256.o(i.ADS1256ReadData) for ADS1256ReadData
    user.o(i.Change_Menu) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.Change_Menu) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.Change_Menu) refers to user.o(.data) for menu_flag
    user.o(i.Change_Menu) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.DAC_setval) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.DAC_setval) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.DAC_setval) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.DAC_setval) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user.o(i.DAC_setval) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    user.o(i.Disp_Main) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.Disp_Main) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user.o(i.GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user.o(i.Get_FreSpectrum) refers to drive_fft.o(i.fft_process) for fft_process
    user.o(i.Get_FreSpectrum) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.Get_FreSpectrum) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    user.o(i.Get_FreSpectrum) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.Get_FreSpectrum) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    user.o(i.Get_FreSpectrum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.Get_FreSpectrum) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.Init_All) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    user.o(i.Init_All) refers to user_adc.o(i.User_ADC_Init) for User_ADC_Init
    user.o(i.Init_All) refers to user_dac.o(i.User_DAC_Init) for User_DAC_Init
    user.o(i.Init_All) refers to user.o(i.GPIO_Config) for GPIO_Config
    user.o(i.Init_All) refers to user_pga2310.o(i.PGA2310_Init) for PGA2310_Init
    user.o(i.Init_All) refers to drive_communication.o(i.Init_Uart) for Init_Uart
    user.o(i.Init_All) refers to drive_communication.o(i.DDSDataInit) for DDSDataInit
    user.o(i.Init_All) refers to drive_ads1256.o(i.ADS1256_Init) for ADS1256_Init
    user.o(i.Init_All) refers to user_adc.o(i.IIR_Init) for IIR_Init
    user.o(i.Init_All) refers to user_adc.o(i.PC1_Init) for PC1_Init
    user.o(i.MenuHaddler_1) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.MenuHaddler_1) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.MenuHaddler_1) refers to user.o(i.AD9959_senddata) for AD9959_senddata
    user.o(i.MenuHaddler_1) refers to stm32f4xx_dac.o(i.DAC_SetChannel2Data) for DAC_SetChannel2Data
    user.o(i.MenuHaddler_1) refers to user.o(i.PS2_ReadNum) for PS2_ReadNum
    user.o(i.MenuHaddler_1) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.MenuHaddler_1) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.MenuHaddler_1) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    user.o(i.MenuHaddler_1) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.MenuHaddler_1) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.MenuHaddler_1) refers to drive_communication.o(.bss) for dds
    user.o(i.MenuHaddler_1) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    user.o(i.MenuHaddler_1) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.MenuHaddler_1) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    user.o(i.MenuHaddler_1) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user.o(i.MenuHaddler_1) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.MenuHaddler_1) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_2) refers to stm32f4xx_dac.o(i.DAC_SetChannel2Data) for DAC_SetChannel2Data
    user.o(i.MenuHaddler_2) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.MenuHaddler_2) refers to user.o(i.AD9959_senddata) for AD9959_senddata
    user.o(i.MenuHaddler_2) refers to user.o(i.set_sweep) for set_sweep
    user.o(i.MenuHaddler_2) refers to user.o(i.circulate_Av) for circulate_Av
    user.o(i.MenuHaddler_2) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.MenuHaddler_2) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    user.o(i.MenuHaddler_2) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    user.o(i.MenuHaddler_2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.MenuHaddler_2) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.MenuHaddler_2) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.MenuHaddler_2) refers to drive_communication.o(.bss) for dds
    user.o(i.MenuHaddler_2) refers to user.o(.bss) for SweepValue
    user.o(i.MenuHaddler_2) refers to user.o(.data) for decrease_db
    user.o(i.MenuHaddler_2) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    user.o(i.MenuHaddler_2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    user.o(i.MenuHaddler_2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.MenuHaddler_2) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_3) refers to user_adc.o(i.IIR_Filter_Enable) for IIR_Filter_Enable
    user.o(i.MenuHaddler_3) refers to user_adc.o(i.IIR_Filter_Config) for IIR_Filter_Config
    user.o(i.MenuHaddler_3) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.MenuHaddler_3) refers to user_adc.o(i.Set_SamplingFre) for Set_SamplingFre
    user.o(i.MenuHaddler_3) refers to user_adc.o(i.Start_ADC_Sampling) for Start_ADC_Sampling
    user.o(i.MenuHaddler_3) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.MenuHaddler_3) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_3) refers to user.o(.data) for a2
    user.o(i.MenuHaddler_3) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.MenuHaddler_4) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_4) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.My_Disp_Main) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.My_Disp_Main) refers to tft_lcd.o(i.LCD_DrawRect) for LCD_DrawRect
    user.o(i.My_Disp_Main) refers to user.o(.data) for menu_flag
    user.o(i.PS2_ReadNum) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user.o(i.PS2_ReadNum) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.PS2_ReadNum) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    user.o(i.PS2_ReadNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    user.o(i.PS2_ReadNum) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    user.o(i.PS2_ReadNum) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.PS2_ReadNum) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.PS2_ReadNum) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user.o(i.PS2_ReadNum) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.PS2_ReadNum) refers to os_ui.o(i.OS_Num_Show) for OS_Num_Show
    user.o(i.PS2_ReadNum) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.Show_Val) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.Show_Val) refers to os_ui.o(i.OS_Num_Show) for OS_Num_Show
    user.o(i.Show_Val) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.User_Data_PlotAxis) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    user.o(i.User_Data_PlotAxis) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    user.o(i.User_Data_PlotAxis) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    user.o(i.User_Data_PlotAxis) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    user.o(i.User_Data_PlotAxis) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.User_Data_PlotAxis) refers to noretval__2sprintf.o(.text) for __2sprintf
    user.o(i.User_Data_PlotAxis) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.User_Data_PlotAxisSimple) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    user.o(i.User_Data_PlotAxisSimple) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.User_Data_PlotClear) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.User_Data_PlotDrawScaled) refers to user.o(i.User_Data_MapToRow) for User_Data_MapToRow
    user.o(i.User_Data_PlotDrawScaled) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    user.o(i.User_Data_PlotDrawScaled) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    user.o(i.User_GetSignalInf) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    user.o(i.User_GetSignalInf) refers to user_adc.o(i.Get_ACVol) for Get_ACVol
    user.o(i.User_GetSignalInf) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.User_GetSignalInf) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.User_GetSignalInf) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.User_GetSignalInf) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user.o(i.User_GetSignalInf) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user.o(i.User_GetSignalInf) refers to user.o(i.Get_FreSpectrum) for Get_FreSpectrum
    user.o(i.User_GetSignalInf) refers to user.o(i.User_FixPhase) for User_FixPhase
    user.o(i.User_GetSignalInf) refers to user_dac.o(.bss) for WaveData
    user.o(i.User_main) refers to user.o(i.Init_All) for Init_All
    user.o(i.User_main) refers to user.o(i.My_Disp_Main) for My_Disp_Main
    user.o(i.User_main) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.User_main) refers to user.o(i.menu_show) for menu_show
    user.o(i.User_main) refers to user.o(i.MenuHaddler_1) for MenuHaddler_1
    user.o(i.User_main) refers to user.o(i.MenuHaddler_2) for MenuHaddler_2
    user.o(i.User_main) refers to user.o(i.MenuHaddler_3) for MenuHaddler_3
    user.o(i.User_main) refers to user.o(i.MenuHaddler_4) for MenuHaddler_4
    user.o(i.User_main) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.User_main) refers to user.o(.data) for MenuSign
    user.o(i.User_main) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.circulate_Av) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.circulate_Av) refers to drive_ads1256.o(i.ADS1256_Init) for ADS1256_Init
    user.o(i.circulate_Av) refers to drive_ads1256.o(i.Moving_Average_Filter) for Moving_Average_Filter
    user.o(i.circulate_Av) refers to drive_ads1256.o(i.Get_Val) for Get_Val
    user.o(i.circulate_Av) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.circulate_Av) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.circulate_Av) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    user.o(i.circulate_Av) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.circulate_Av) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.dds_show) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.dds_show) refers to drive_communication.o(.bss) for dds
    user.o(i.menu_show) refers to user.o(i.My_Disp_Main) for My_Disp_Main
    user.o(i.menu_show) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.menu_show) refers to user.o(.data) for menu_flag
    user.o(i.set_sweep) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    user.o(i.set_sweep) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.set_sweep) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    user.o(i.set_sweep) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.set_sweep) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.set_sweep) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    user.o(i.set_sweep) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.set_sweep) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user.o(i.set_sweep) refers to user.o(.bss) for SweepValue
    main.o(i.OS_Init) refers to os_cpu.o(i.System_init) for System_init
    main.o(i.OS_Init) refers to drive_gpio.o(i.LED_Init) for LED_Init
    main.o(i.OS_Init) refers to drive_ps2.o(i.PS2_Keyboard_Init) for PS2_Keyboard_Init
    main.o(i.OS_Init) refers to os_ui.o(i.OS_LCD_Init) for OS_LCD_Init
    main.o(i.main) refers to os_cpu.o(i.Task_Create) for Task_Create
    main.o(i.main) refers to main.o(i.OS_Init) for OS_Init
    main.o(i.main) refers to os_cpu.o(i.OS_Start) for OS_Start
    main.o(i.main) refers to main.o(.bss) for TASK_0_STK
    main.o(i.main) refers to user.o(i.User_main) for User_main
    main.o(i.main) refers to drive_ps2.o(i.MyPs2KeyScan) for MyPs2KeyScan
    main.o(i.main) refers to app_led.o(i.LED_main) for LED_main
    os_cpu.o(i.OSGetHighRdy) refers to os_cpu.o(.data) for OSRdyTbl
    os_cpu.o(i.OSTaskRecovery) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(i.OSSetPrioRdy) for OSSetPrioRdy
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(.data) for OSRdyTbl
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(.bss) for TCB_Task
    os_cpu.o(i.OSTaskSuspend) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(i.OSDelPrioRdy) for OSDelPrioRdy
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(.data) for OS_PrioCur
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(.bss) for TCB_Task
    os_cpu.o(i.OSTimeDly) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(i.OSDelPrioRdy) for OSDelPrioRdy
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(.data) for OS_PrioCur
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(.bss) for TCB_Task
    os_cpu.o(i.OS_Sched) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OS_Sched) refers to os_cpu.o(i.OSGetHighRdy) for OSGetHighRdy
    os_cpu.o(i.OS_Sched) refers to os_cpu.o(.data) for Sched_flag
    os_cpu.o(i.OS_Sched) refers to os_cpu.o(.bss) for TCB_Task
    os_cpu.o(i.OS_SchedLock) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OS_SchedLock) refers to os_cpu.o(.data) for Sched_flag
    os_cpu.o(i.OS_SchedUnlock) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OS_SchedUnlock) refers to os_cpu.o(.data) for Sched_flag
    os_cpu.o(i.OS_Start) refers to os_cpu.o(i.Task_Create) for Task_Create
    os_cpu.o(i.OS_Start) refers to os_cpu.o(i.OSGetHighRdy) for OSGetHighRdy
    os_cpu.o(i.OS_Start) refers to core.o(CODE) for OSStartHighRdy
    os_cpu.o(i.OS_Start) refers to os_cpu.o(.data) for OS_Running
    os_cpu.o(i.OS_Start) refers to os_cpu.o(.bss) for CPU_ExceptStk
    os_cpu.o(i.OS_Start) refers to os_cpu.o(i.OS_IDLE_Task) for OS_IDLE_Task
    os_cpu.o(i.SysTick_Handler) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(i.OSSetPrioRdy) for OSSetPrioRdy
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(.data) for OS_Running
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(.bss) for TCB_Task
    os_cpu.o(i.System_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    os_cpu.o(i.System_init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    os_cpu.o(i.System_init) refers to os_cpu.o(.data) for fac_us
    os_cpu.o(i.Task_Create) refers to os_cpu.o(i.OSSetPrioRdy) for OSSetPrioRdy
    os_cpu.o(i.Task_Create) refers to os_cpu.o(i.Task_End) for Task_End
    os_cpu.o(i.Task_Create) refers to os_cpu.o(.bss) for TCB_Task
    os_ui.o(i.OS_BackColor_Set) refers to tft_lcd.o(.data) for BackColor
    os_ui.o(i.OS_Char_Show) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Char_Show) refers to fonts.o(.constdata) for asc2_1608
    os_ui.o(i.OS_Char_Show) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Circle_Draw) refers to tft_lcd.o(i.LCD_DrawCircleS) for LCD_DrawCircleS
    os_ui.o(i.OS_Circle_Draw) refers to tft_lcd.o(i.LCD_DrawCircle) for LCD_DrawCircle
    os_ui.o(i.OS_Circle_Draw) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Font_Show) refers to os_ui.o(i.OS_HzMat_Get) for OS_HzMat_Get
    os_ui.o(i.OS_Font_Show) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Font_Show) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_HzMat_Get) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    os_ui.o(i.OS_HzMat_Get) refers to fontupd.o(.bss) for ftinfo
    os_ui.o(i.OS_LCD_Clear) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    os_ui.o(i.OS_LCD_Init) refers to tft_lcd.o(i.TFT_LCD_Init) for TFT_LCD_Init
    os_ui.o(i.OS_LCD_Init) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    os_ui.o(i.OS_LCD_Init) refers to fontupd.o(i.font_init) for font_init
    os_ui.o(i.OS_LCD_Init) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    os_ui.o(i.OS_LCD_Init) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Line_Draw) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Num_Show) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    os_ui.o(i.OS_Num_Show) refers to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    os_ui.o(i.OS_Num_Show) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    os_ui.o(i.OS_Num_Show) refers to _printf_pad.o(.text) for _printf_pre_padding
    os_ui.o(i.OS_Num_Show) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    os_ui.o(i.OS_Num_Show) refers to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    os_ui.o(i.OS_Num_Show) refers to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    os_ui.o(i.OS_Num_Show) refers to printf2.o(x$fpl$printf2) for _printf_fp_hex
    os_ui.o(i.OS_Num_Show) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    os_ui.o(i.OS_Num_Show) refers to noretval__2sprintf.o(.text) for __2sprintf
    os_ui.o(i.OS_Num_Show) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Picture_Draw) refers to os_ui.o(.data) for UeseState
    os_ui.o(i.OS_Point_Draw) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    os_ui.o(i.OS_Point_Draw) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    os_ui.o(i.OS_Point_Draw) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    os_ui.o(i.OS_Rect_Draw) refers to tft_lcd.o(i.LCD_DrawRectS) for LCD_DrawRectS
    os_ui.o(i.OS_Rect_Draw) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    os_ui.o(i.OS_Rect_Draw) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_String_Show) refers to os_ui.o(i.OS_Char_Show) for OS_Char_Show
    os_ui.o(i.OS_String_Show) refers to os_ui.o(i.OS_Font_Show) for OS_Font_Show
    os_ui.o(i.OS_TextColor_Set) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Wave_Draw) refers to os_ui.o(i.OS_Wave_Windows_Show) for OS_Wave_Windows_Show
    os_ui.o(i.OS_Wave_Draw) refers to os_ui.o(i.OS_Wave_Line_Show) for OS_Wave_Line_Show
    os_ui.o(i.OS_Wave_Line_Show) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    os_ui.o(i.OS_Wave_Line_Show) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Wave_Line_Show) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    os_ui.o(i.OS_Wave_Line_Show) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    os_ui.o(i.OS_Wave_Line_Show) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    os_ui.o(i.OS_Wave_Line_Show) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    os_ui.o(i.OS_Wave_Line_Show) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    os_ui.o(i.OS_Wave_Line_Show) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    os_ui.o(i.OS_Wave_Windows_Show) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    os_ui.o(i.OS_Wave_Windows_Show) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    os_malloc.o(i.my_mem_free) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(i.my_mem_free) refers to os_malloc.o(.constdata) for memsize
    os_malloc.o(i.my_mem_init) refers to os_malloc.o(i.mymemset) for mymemset
    os_malloc.o(i.my_mem_init) refers to os_malloc.o(.constdata) for memtblsize
    os_malloc.o(i.my_mem_init) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(i.my_mem_malloc) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(i.my_mem_malloc) refers to os_malloc.o(.constdata) for memblksize
    os_malloc.o(i.my_mem_perused) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(i.my_mem_perused) refers to os_malloc.o(.constdata) for memtblsize
    os_malloc.o(i.myfree) refers to core.o(CODE) for OS_CPU_SR_Save
    os_malloc.o(i.myfree) refers to os_malloc.o(i.my_mem_free) for my_mem_free
    os_malloc.o(i.myfree) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(i.mymalloc) refers to core.o(CODE) for OS_CPU_SR_Save
    os_malloc.o(i.mymalloc) refers to os_malloc.o(i.my_mem_malloc) for my_mem_malloc
    os_malloc.o(i.mymalloc) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(i.myrealloc) refers to core.o(CODE) for OS_CPU_SR_Save
    os_malloc.o(i.myrealloc) refers to os_malloc.o(i.my_mem_malloc) for my_mem_malloc
    os_malloc.o(i.myrealloc) refers to os_malloc.o(i.mymemcpy) for mymemcpy
    os_malloc.o(i.myrealloc) refers to os_malloc.o(i.myfree) for myfree
    os_malloc.o(i.myrealloc) refers to os_malloc.o(.data) for mallco_dev
    os_malloc.o(.data) refers to os_malloc.o(i.my_mem_init) for my_mem_init
    os_malloc.o(.data) refers to os_malloc.o(i.my_mem_perused) for my_mem_perused
    os_malloc.o(.data) refers to os_malloc.o(.bss) for mem1base
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x68000000) for mem2base
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x10000000) for mem3base
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x680F0000) for mem2mapbase
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x1000F000) for mem3mapbase
    core.o(CODE) refers to os_cpu.o(.data) for CPU_ExceptStkBase
    delay.o(i.delay_ms) refers to os_cpu.o(i.OSTimeDly) for OSTimeDly
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_ms) refers to os_cpu.o(.data) for OS_Running
    delay.o(i.delay_us) refers to os_cpu.o(i.OS_SchedLock) for OS_SchedLock
    delay.o(i.delay_us) refers to os_cpu.o(i.OS_SchedUnlock) for OS_SchedUnlock
    delay.o(i.delay_us) refers to os_cpu.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    drive_gpio.o(i.GPIO_Data_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.GPIO_Data_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_gpio.o(i.GPIO_Key_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.GPIO_Key_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_gpio.o(i.GPIO_POW_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.GPIO_POW_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_gpio.o(i.GPIO_POW_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_gpio.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    drive_dma.o(i.ADC1_DMA2_Reload) refers to drive_dma.o(.bss) for ADC1_DMA2_Buff
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    drive_dma.o(i.ADC3_DMA2_Init) refers to drive_dma.o(.bss) for ADC3_DMA2_Buff
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    drive_dma.o(i.ADC3_DMA2_Reload) refers to drive_dma.o(.bss) for ADC3_DMA2_Buff
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to drive_dma.o(i.DAC1_DMA1_Reload) for DAC1_DMA1_Reload
    drive_dma.o(i.DAC1_DMA1_Init) refers to drive_dma.o(.data) for DAC1_DMA1_Buff
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM10_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM11_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM12_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM13_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM14_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    drive_timer.o(i.TIM1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM4_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM5_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM6_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM7_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM8_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM9_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_touch.o(i.ADS_Read_AD) refers to drive_touch.o(i.ADS_Write_Byte) for ADS_Write_Byte
    drive_touch.o(i.ADS_Read_AD) refers to delay.o(i.delay_us) for delay_us
    drive_touch.o(i.ADS_Read_AD) refers to drive_touch.o(i.Delay) for Delay
    drive_touch.o(i.ADS_Read_XY) refers to drive_touch.o(i.ADS_Read_AD) for ADS_Read_AD
    drive_touch.o(i.ADS_Write_Byte) refers to drive_touch.o(i.Delay) for Delay
    drive_touch.o(i.GPIO_Configuration) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_touch.o(i.GPIO_Configuration) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_touch.o(i.TouchRead) refers to drive_touch.o(i.ADS_Read_XY) for ADS_Read_XY
    drive_touch.o(i.Touch_Init) refers to drive_touch.o(i.GPIO_Configuration) for GPIO_Configuration
    drive_touchkey.o(i.Clear_Show) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    drive_touchkey.o(i.Clear_Show) refers to drive_touchkey.o(.data) for shift_y
    drive_touchkey.o(i.Interface) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    drive_touchkey.o(i.Interface) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    drive_touchkey.o(i.Interface) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    drive_touchkey.o(i.Interface) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_touchkey.o(i.Interface) refers to os_ui.o(i.OS_Num_Show) for OS_Num_Show
    drive_touchkey.o(i.TouchKey_Draw) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    drive_touchkey.o(i.TouchKey_Draw) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    drive_touchkey.o(i.TouchKey_Draw) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    drive_touchkey.o(i.TouchKey_Draw) refers to drive_touchkey.o(.data) for shift_y
    drive_touchkey.o(i.TouchKey_Scan) refers to app_touch.o(i.Touch_Judge) for Touch_Judge
    drive_touchkey.o(i.TouchKey_Scan) refers to drive_touchkey.o(i.Clear_Show) for Clear_Show
    drive_touchkey.o(i.TouchKey_Scan) refers to drive_touchkey.o(.data) for shift_y
    drive_touchkey.o(i.TouchKey_Scan) refers to drive_touchkey.o(.bss) for databuf
    drive_touchkey.o(i.TouchKey_Scan) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    drive_touchkey.o(i.TouchKey_Scan) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    drive_touchkey.o(i.TouchKey_Scan) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_pwm.o(i.PWM1_CCR_Set) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_pwm.o(i.PWM1_CCR_Set) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_pwm.o(i.PWM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    user_spi.o(i.SPI_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_spi.o(i.SPI_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    user_spi.o(i.SPI_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_spi.o(i.User_SPI_Init) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_spi.o(i.User_SPI_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_spi.o(i.User_SPI_Init) refers to stm32f4xx_spi.o(i.SPI_Init) for SPI_Init
    user_spi.o(i.User_SPI_SendData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_spi.o(i.User_SPI_SendData) refers to delay.o(i.delay_us) for delay_us
    user_spi.o(i.User_SPI_SendData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_bgd.o(i.Check_BGD) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    user_bgd.o(i.Check_BGD) refers to user_bgd.o(i.Grad_Descent) for Grad_Descent
    user_bgd.o(i.Check_BGD) refers to user_bgd.o(.constdata) for .constdata
    user_bgd.o(i.Grad_Descent) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    user_bgd.o(i.Grad_Descent) refers to user_bgd.o(i.GetSum) for GetSum
    user_bgd.o(i.Grad_Descent) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_bgd.o(i.Grad_Descent) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user_bgd.o(i.Grad_Descent) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_ps2.o(i.Key_StateSweep) refers to drive_ps2.o(i.PS2_ReadKeyCodon) for PS2_ReadKeyCodon
    drive_ps2.o(i.MyPs2KeyScan) refers to drive_ps2.o(i.Key_StateSweep) for Key_StateSweep
    drive_ps2.o(i.MyPs2KeyScan) refers to delay.o(i.delay_ms) for delay_ms
    drive_ps2.o(i.MyPs2KeyScan) refers to drive_ps2.o(.bss) for Key_FSM_PS2
    drive_ps2.o(i.MyPs2KeyScan) refers to drive_ps2.o(.data) for Ps2KeyValue
    drive_ps2.o(i.PS2_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_ps2.o(i.PS2_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_ps2.o(i.PS2_Keyboard_Init) refers to drive_ps2.o(i.PS2_GPIO_Init) for PS2_GPIO_Init
    drive_ps2.o(i.PS2_ReadKeyCodon) refers to drive_ps2.o(i.PS2_SCL_Set) for PS2_SCL_Set
    drive_ps2.o(i.PS2_ReadKeyCodon) refers to drive_ps2.o(i.PS2_SCL_Wait) for PS2_SCL_Wait
    drive_ps2.o(i.PS2_ReadKeyCodon) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ps2.o(i.PS2_SCL_Wait) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    drive_ps2.o(i.PS2_SCL_Wait) refers to drive_ps2.o(i.PS2_SCL_Set) for PS2_SCL_Set
    drive_ps2.o(i.PS2_SCL_Wait) refers to drive_ps2.o(.data) for Count
    user_dac8562.o(i.DAC8562_GPIOInit) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_dac8562.o(i.DAC8562_GPIOInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_dac8562.o(i.DAC8562_GPIOInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_dac8562.o(i.DAC8562_Init) refers to user_dac8562.o(i.DAC8562_GPIOInit) for DAC8562_GPIOInit
    user_dac8562.o(i.DAC8562_Init) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_dac8562.o(i.DAC8562_OutAC) refers to user_dac8562.o(.data) for DAC8562_Vref
    user_dac8562.o(i.DAC8562_OutDC) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_dac8562.o(i.DAC8562_OutDC) refers to user_dac8562.o(.data) for DAC8562_Vref
    user_dac8562.o(i.Set_ACData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_dac8562.o(i.Set_ACData) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    user_dac8562.o(i.Set_ACData) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user_dac8562.o(i.Set_ACData) refers to user_dac8562.o(.data) for DAC8562_Vref
    user_dac8562.o(i.Set_ACData) refers to user_dac8562.o(.bss) for ACData
    user_ad8370.o(i.AD8370_GPIOInit) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_ad8370.o(i.AD8370_GPIOInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_ad8370.o(i.AD8370_GPIOInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_ad8370.o(i.AD8370_Init) refers to user_ad8370.o(i.AD8370_GPIOInit) for AD8370_GPIOInit
    user_ad8370.o(i.AD8370_Init) refers to user_ad8370.o(i.AD8370_SetTimes) for AD8370_SetTimes
    user_ad8370.o(i.AD8370_SetTimes) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_ad8370.o(i.AD8370_SetTimes) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user_ad8370.o(i.AD8370_SetTimes) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user_ad8370.o(i.AD8370_SetTimes) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_ad8370.o(i.AD8370_SetTimes) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_ad8370.o(i.AD8370_SetTimes) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_iic.o(i.IIC_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_iic.o(i.IIC_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_iic.o(i.IIC_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_SendData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_SendData) refers to delay.o(i.delay_us) for delay_us
    user_iic.o(i.IIC_SendData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_iic.o(i.IIC_SendData) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    user_iic.o(i.IIC_Start) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_iic.o(i.IIC_Start) refers to delay.o(i.delay_us) for delay_us
    user_iic.o(i.IIC_Start) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_Stop) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    user_iic.o(i.IIC_Stop) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_communication.o(i.DDSDataInit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    drive_communication.o(i.DDSDataInit) refers to drive_communication.o(i.sendData) for sendData
    drive_communication.o(i.DDSDataInit) refers to delay.o(i.delay_ms) for delay_ms
    drive_communication.o(i.DDSDataInit) refers to drive_communication.o(.bss) for dds
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    drive_communication.o(i.Init_Uart_6) refers to stm32f4xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    drive_communication.o(i.USART6_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    drive_communication.o(i.USART6_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    drive_communication.o(i.USART6_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    drive_communication.o(i.sendData) refers to drive_communication.o(i.crc_16) for crc_16
    drive_communication.o(i.sendData) refers to drive_communication.o(i.usartSendData) for usartSendData
    drive_communication.o(i.sendData) refers to drive_communication.o(.bss) for communicationData
    drive_communication.o(i.usartSendData) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    drive_communication.o(i.usartSendData) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    drive_ads1256.o(i.ADS1256RREG) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256RREG) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256RREG) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256RREG) refers to drive_ads1256.o(i.SPI_ReadByte) for SPI_ReadByte
    drive_ads1256.o(i.ADS1256RREG) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.ADS1256ReadData) refers to drive_ads1256.o(i.ADS1256WREG) for ADS1256WREG
    drive_ads1256.o(i.ADS1256ReadData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256ReadData) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256ReadData) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256ReadData) refers to drive_ads1256.o(i.SPI_ReadByte) for SPI_ReadByte
    drive_ads1256.o(i.ADS1256ReadData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.ADS1256WREG) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256WREG) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256WREG) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256WREG) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.ADS1256_Init) refers to drive_ads1256.o(i.Init_ADS1256_GPIO) for Init_ADS1256_GPIO
    drive_ads1256.o(i.ADS1256_Init) refers to delay.o(i.delay_ms) for delay_ms
    drive_ads1256.o(i.ADS1256_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256_Init) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256_Init) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256_Init) refers to drive_ads1256.o(i.ADS1256WREG) for ADS1256WREG
    drive_ads1256.o(i.ADS1256_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.Init_ADS1256_GPIO) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_ads1256.o(i.Init_ADS1256_GPIO) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_ads1256.o(i.Init_ADS1256_GPIO) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.Moving_Average_Filter) refers to drive_ads1256.o(i.ADS1256ReadData) for ADS1256ReadData
    drive_ads1256.o(i.SPI_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.SPI_ReadByte) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.SPI_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.SPI_WriteByte) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.SPI_WriteByte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.SPI_WriteByte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_fft.o(i.Wn_i) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_fft.o(i.Wn_i) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    drive_fft.o(i.Wn_i) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_fft.o(i.Wn_i) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    drive_fft.o(i.Wn_i) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    drive_fft.o(i.c_abs) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_fft.o(i.c_abs) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    drive_fft.o(i.c_abs) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_fft.o(i.complex_abs_float) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_fft.o(i.complex_abs_float) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    drive_fft.o(i.complex_abs_float) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_fft.o(i.fft) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    drive_fft.o(i.fft) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    drive_fft.o(i.fft) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    drive_fft.o(i.fft) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_fft.o(i.fft) refers to drive_fft.o(i.Wn_i) for Wn_i
    drive_fft.o(i.fft) refers to drive_fft.o(i.c_mul) for c_mul
    drive_fft.o(i.fft) refers to drive_fft.o(i.c_sub) for c_sub
    drive_fft.o(i.fft) refers to drive_fft.o(i.c_plus) for c_plus
    drive_fft.o(i.fft_process) refers to arm_cfft_radix2_init_f32.o(.text) for arm_cfft_radix2_init_f32
    drive_fft.o(i.fft_process) refers to arm_cfft_radix2_f32.o(.text) for arm_cfft_radix2_f32
    drive_fft.o(i.fft_process) refers to drive_fft.o(.bss) for fft_instance
    drive_fft.o(i.ifft) refers to drive_fft.o(i.conjugate_complex) for conjugate_complex
    drive_fft.o(i.ifft) refers to drive_fft.o(i.fft) for fft
    user_pga2310.o(i.PGA2310_Init) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_pga2310.o(i.PGA2310_Init) refers to user_pga2310.o(i.PGA2310_SetAv) for PGA2310_SetAv
    user_pga2310.o(i.PGA2310_SetAv) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_pga2310.o(i.PGA2310_SetAv) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    user_pga2310.o(i.PGA2310_SetAv) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    user_pga2310.o(i.PGA2310_SetAv) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_dac.o(i.DAC1_Vol_Set) refers to user_dac.o(.data) for coefficent
    user_dac.o(i.Set_TriggerFre) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_dac.o(i.Set_WaveData) refers to user_dac.o(i.User_DAC_DMA_Init) for User_DAC_DMA_Init
    user_dac.o(i.Set_WaveData) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    user_dac.o(i.Set_WaveData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_dac.o(i.Set_WaveData) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user_dac.o(i.Set_WaveData) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user_dac.o(i.Set_WaveData) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user_dac.o(i.Set_WaveData) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user_dac.o(i.Set_WaveData) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user_dac.o(i.Set_WaveData) refers to user_dac.o(.data) for coefficent
    user_dac.o(i.Set_WaveData) refers to user_dac.o(.bss) for WaveData
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_DeInit) for DAC_DeInit
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_StructInit) for DAC_StructInit
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    user_dac.o(i.User_DAC_DMA_Init) refers to user_dac.o(.bss) for WaveData
    user_dac.o(i.User_DAC_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_dac.o(i.User_DAC_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_GPIO_Init) for User_DAC_GPIO_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_TIM_Init) for User_DAC_TIM_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_DMA_Init) for User_DAC_DMA_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_Configure) for User_DAC_Configure
    user_dac.o(i.User_DAC_Init) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) for TIM_UpdateDisableConfig
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_DMACmd) for TIM_DMACmd
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_dac.o(i.User_DAC_TIM_Init) refers to user_dac.o(i.Set_TriggerFre) for Set_TriggerFre
    user_dac.o(i.User_DAC_TIM_NVIC_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    user_dac.o(i.User_DAC_TIM_NVIC_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    drive_dac.o(i.dacClose) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.dacClose) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    drive_dac.o(i.dacInit) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dac.o(i.dacInit) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dac.o(i.dacInit) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd) for DAC_SoftwareTriggerCmd
    drive_dac.o(i.dacInit) refers to drive_dac.o(i.ddsDataInit) for ddsDataInit
    drive_dac.o(i.dacInit) refers to drive_dac.o(.bss) for ddsStructData
    drive_dac.o(i.dacInit) refers to drive_dac.o(.data) for firstInitFlag
    drive_dac.o(i.dacOpen) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.dacOpen) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_dac.o(i.ddsDataInit) refers to drive_dac.o(.bss) for ddsStructData
    drive_dac.o(i.ddsDataInit) refers to drive_dac.o(i.ddsSinWave) for ddsSinWave
    drive_dac.o(i.ddsSawtoothWave) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.ddsSawtoothWave) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.ddsSawtoothWave) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.ddsSawtoothWave) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_dac.o(i.ddsSinWave) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.ddsSinWave) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.ddsSinWave) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.ddsSinWave) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    drive_dac.o(i.ddsSquareWave) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.ddsSquareWave) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.ddsSquareWave) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.ddsSquareWave) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    drive_dac.o(i.ddsTriangleWave) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_dac.o(i.setDDS) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.dacInit) for dacInit
    drive_dac.o(i.setDDS) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_dac.o(i.setDDS) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.setDDS) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.setDDS) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    drive_dac.o(i.setDDS) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.timer6Init) for timer6Init
    drive_dac.o(i.setDDS) refers to drive_dac.o(.bss) for ddsStructData
    drive_dac.o(i.setDDS) refers to drive_dac.o(.data) for preLength
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsSinWave) for ddsSinWave
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsTriangleWave) for ddsTriangleWave
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsSawtoothWave) for ddsSawtoothWave
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsSquareWave) for ddsSquareWave
    drive_dac.o(i.timer6Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dac.o(i.timer6Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_dac.o(i.timer6Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    drive_dac.o(i.timer6Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_flash.o(i.InFLASH_Read) refers to drive_flash.o(i.InFLASH_ReadWord) for InFLASH_ReadWord
    drive_flash.o(i.InFLASH_Write) refers to stm32f4xx_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    drive_flash.o(i.InFLASH_Write) refers to stm32f4xx_flash.o(i.FLASH_DataCacheCmd) for FLASH_DataCacheCmd
    drive_flash.o(i.InFLASH_Write) refers to drive_flash.o(i.InFLASH_ReadWord) for InFLASH_ReadWord
    drive_flash.o(i.InFLASH_Write) refers to drive_flash.o(i.InFLASH_GetFlashSector) for InFLASH_GetFlashSector
    drive_flash.o(i.InFLASH_Write) refers to stm32f4xx_flash.o(i.FLASH_EraseSector) for FLASH_EraseSector
    drive_flash.o(i.InFLASH_Write) refers to stm32f4xx_flash.o(i.FLASH_ProgramWord) for FLASH_ProgramWord
    drive_flash.o(i.InFLASH_Write) refers to stm32f4xx_flash.o(i.FLASH_Lock) for FLASH_Lock
    character.o(i.LCD_GB1616) refers to tft_lcd.o(i.PutPixel) for PutPixel
    character.o(i.LCD_GB1616) refers to character.o(.constdata) for codeGB_16
    character.o(i.LCD_GB3232) refers to tft_lcd.o(i.PutPixel) for PutPixel
    character.o(i.LCD_GB3232) refers to character.o(.constdata) for codeGB_32
    character.o(i.LCD_GB4848) refers to tft_lcd.o(i.PutPixel) for PutPixel
    character.o(i.LCD_GB4848) refers to character.o(.constdata) for codeGB_48
    character.o(i.Show_Str32) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    character.o(i.Show_Str32) refers to character.o(i.LCD_GB3232) for LCD_GB3232
    character.o(i.Show_Str48) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    character.o(i.Show_Str48) refers to character.o(i.LCD_GB4848) for LCD_GB4848
    fonts.o(.data) refers to fonts.o(.constdata) for Font_1608
    fontupd.o(i.font_init) refers to w25q64.o(i.W25Q64_Init) for W25Q64_Init
    fontupd.o(i.font_init) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    fontupd.o(i.font_init) refers to delay.o(i.delay_ms) for delay_ms
    fontupd.o(i.font_init) refers to fontupd.o(.bss) for ftinfo
    spi.o(i.SPI1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    spi.o(i.SPI1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    spi.o(i.SPI1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    spi.o(i.SPI1_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    spi.o(i.SPI1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    spi.o(i.SPI1_Init) refers to stm32f4xx_spi.o(i.SPI_Init) for SPI_Init
    spi.o(i.SPI1_Init) refers to stm32f4xx_spi.o(i.SPI_Cmd) for SPI_Cmd
    spi.o(i.SPI1_Init) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_spi.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) for SPI_I2S_ReceiveData
    spi.o(i.SPI1_SetSpeed) refers to stm32f4xx_spi.o(i.SPI_Cmd) for SPI_Cmd
    text.o(i.Get_HzMat) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    text.o(i.Get_HzMat) refers to fontupd.o(.bss) for ftinfo
    text.o(i.Show_Font) refers to text.o(i.Get_HzMat) for Get_HzMat
    text.o(i.Show_Font) refers to tft_lcd.o(i.PutPixel) for PutPixel
    tft_lcd.o(i.Display_Control) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tft_lcd.o(i.Display_Control) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.LCD_Appoint_Clear) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_Appoint_Clear) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.LCD_Clear) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_Clear) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    tft_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    tft_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    tft_lcd.o(i.LCD_Display0x) refers to tft_lcd.o(i.Display_Control) for Display_Control
    tft_lcd.o(i.LCD_DisplayNum) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tft_lcd.o(i.LCD_DisplayNum) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.LCD_DisplayStringLine) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tft_lcd.o(i.LCD_DisplayStringLine) refers to tft_lcd.o(i.TFT_DispChar) for TFT_DispChar
    tft_lcd.o(i.LCD_DisplayStringLine) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.LCD_Display_FloatNum) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    tft_lcd.o(i.LCD_Display_FloatNum) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tft_lcd.o(i.LCD_Display_FloatNum) refers to tft_lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    tft_lcd.o(i.LCD_Display_FloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tft_lcd.o(i.LCD_Display_FloatNum) refers to dfixull.o(x$fpl$llufromd) for __aeabi_d2ulz
    tft_lcd.o(i.LCD_Display_FloatNum) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    tft_lcd.o(i.LCD_Display_FloatNum) refers to tft_lcd.o(i.LCD_GetFont) for LCD_GetFont
    tft_lcd.o(i.LCD_Display_FloatNum) refers to tft_lcd.o(i.LCD_DisplayNum) for LCD_DisplayNum
    tft_lcd.o(i.LCD_DrawCircle) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_DrawCircle) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.LCD_DrawCircle) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_DrawCircle) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_DrawCircleS) refers to tft_lcd.o(i.LCD_DrawCircle) for LCD_DrawCircle
    tft_lcd.o(i.LCD_DrawCircleS) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_DrawLine) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_DrawLine) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.LCD_DrawLine) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_DrawLine) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_DrawPoint) refers to tft_lcd.o(i.LCD_REG_Select) for LCD_REG_Select
    tft_lcd.o(i.LCD_DrawPoint) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_DrawPoint_4) refers to tft_lcd.o(i.LCD_REG_Select) for LCD_REG_Select
    tft_lcd.o(i.LCD_DrawPoint_4) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_DrawRect) refers to tft_lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    tft_lcd.o(i.LCD_DrawRectS) refers to tft_lcd.o(i.LCD_DrawuniLine) for LCD_DrawuniLine
    tft_lcd.o(i.LCD_DrawRectS) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_DrawuniLine) refers to tft_lcd.o(i.PutPixel) for PutPixel
    tft_lcd.o(i.LCD_FSMCConfig) refers to stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) for RCC_AHB3PeriphClockCmd
    tft_lcd.o(i.LCD_FSMCConfig) refers to stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    tft_lcd.o(i.LCD_FSMCConfig) refers to stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    tft_lcd.o(i.LCD_GetColors) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_GetFont) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.LCD_SetBackColor) refers to tft_lcd.o(.data) for BackColor
    tft_lcd.o(i.LCD_SetColors) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_SetCursor) refers to tft_lcd.o(i.LCD_REG_Select) for LCD_REG_Select
    tft_lcd.o(i.LCD_SetCursor) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_SetDisplayWindow) refers to tft_lcd.o(i.LCD_REG_Select) for LCD_REG_Select
    tft_lcd.o(i.LCD_SetDisplayWindow) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_SetFont) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.LCD_SetTextColor) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.LCD_ShowChar) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_ShowChar) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.LCD_ShowChar) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.LCD_ShowChar) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.LCD_WeBMP_SIZE) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.LCD_WeBMP_SIZE) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.PutPixel) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.PutPixel) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.PutPixel) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.PutPixel) refers to tft_lcd.o(.data) for TextColor
    tft_lcd.o(i.TFT_DispChar) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.TFT_DispChar) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    tft_lcd.o(i.TFT_DispChar) refers to tft_lcd.o(.data) for LCD_Currentfonts
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_CtrlLinesConfig) for LCD_CtrlLinesConfig
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_FSMCConfig) for LCD_FSMCConfig
    tft_lcd.o(i.TFT_LCD_Init) refers to delay.o(i.delay_ms) for delay_ms
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_REG_Select) for LCD_REG_Select
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    tft_lcd.o(.data) refers to fonts.o(.data) for Font32x16
    w25q64.o(i.W25Q64_Erase_Chip) refers to w25q64.o(i.W25Q64_Write_Enable) for W25Q64_Write_Enable
    w25q64.o(i.W25Q64_Erase_Chip) refers to w25q64.o(i.W25Q64_Wait_Busy) for W25Q64_Wait_Busy
    w25q64.o(i.W25Q64_Erase_Chip) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Erase_Chip) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Erase_Chip) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Erase_Sector) refers to w25q64.o(i.W25Q64_Write_Enable) for W25Q64_Write_Enable
    w25q64.o(i.W25Q64_Erase_Sector) refers to w25q64.o(i.W25Q64_Wait_Busy) for W25Q64_Wait_Busy
    w25q64.o(i.W25Q64_Erase_Sector) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Erase_Sector) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Erase_Sector) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    w25q64.o(i.W25Q64_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    w25q64.o(i.W25Q64_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Init) refers to spi.o(i.SPI1_Init) for SPI1_Init
    w25q64.o(i.W25Q64_Init) refers to spi.o(i.SPI1_SetSpeed) for SPI1_SetSpeed
    w25q64.o(i.W25Q64_Init) refers to w25q64.o(i.W25Q64_ReadID) for W25Q64_ReadID
    w25q64.o(i.W25Q64_Init) refers to w25q64.o(.data) for W25Q64_TYPE
    w25q64.o(i.W25Q64_PowerDown) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_PowerDown) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_PowerDown) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_PowerDown) refers to delay.o(i.delay_us) for delay_us
    w25q64.o(i.W25Q64_Read) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Read) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Read) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_ReadID) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_ReadID) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_ReadID) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_ReadSR) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_ReadSR) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_ReadSR) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_WAKEUP) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_WAKEUP) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_WAKEUP) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_WAKEUP) refers to delay.o(i.delay_us) for delay_us
    w25q64.o(i.W25Q64_Wait_Busy) refers to w25q64.o(i.W25Q64_ReadSR) for W25Q64_ReadSR
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(i.W25Q64_Erase_Sector) for W25Q64_Erase_Sector
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(i.W25Q64_Write_NoCheck) for W25Q64_Write_NoCheck
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(.bss) for W25Q64_BUFFER
    w25q64.o(i.W25Q64_Write_Disable) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_Disable) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_Disable) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Write_Enable) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_Enable) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_Enable) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Write_NoCheck) refers to w25q64.o(i.W25Q64_Write_Page) for W25Q64_Write_Page
    w25q64.o(i.W25Q64_Write_Page) refers to w25q64.o(i.W25Q64_Write_Enable) for W25Q64_Write_Enable
    w25q64.o(i.W25Q64_Write_Page) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_Page) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_Page) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Write_Page) refers to w25q64.o(i.W25Q64_Wait_Busy) for W25Q64_Wait_Busy
    w25q64.o(i.W25Q64_Write_SR) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_SR) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_SR) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to core.o(CODE) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to os_cpu.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to user_adc.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to user_adc.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to drive_communication.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    arm_biquad_cascade_df1_init_f32.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    arm_cfft_radix2_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix2_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix2_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromdr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromdr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    log10.o(i.__hardfp_log10) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.__hardfp_log10) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log10.o(i.__hardfp_log10) refers to _rserrno.o(.text) for __set_errno
    log10.o(i.__hardfp_log10) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log10.o(i.__hardfp_log10) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log10.o(i.__hardfp_log10) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    log10.o(i.__hardfp_log10) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log10.o(i.__hardfp_log10) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log10.o(i.__hardfp_log10) refers to log.o(i.log) for log
    log10.o(i.__hardfp_log10) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log10.o(i.__softfp_log10) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.__softfp_log10) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    log10.o(i.log10) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.log10) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    log10_x.o(i.____hardfp_log10$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10_x.o(i.____hardfp_log10$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log10_x.o(i.____hardfp_log10$lsc) refers to _rserrno.o(.text) for __set_errno
    log10_x.o(i.____hardfp_log10$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log10_x.o(i.____hardfp_log10$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log10_x.o(i.____hardfp_log10$lsc) refers to log.o(i.log) for log
    log10_x.o(i.____hardfp_log10$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log10_x.o(i.____softfp_log10$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10_x.o(i.____softfp_log10$lsc) refers to log10_x.o(i.____hardfp_log10$lsc) for ____hardfp_log10$lsc
    log10_x.o(i.__log10$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10_x.o(i.__log10$lsc) refers to log10_x.o(i.____hardfp_log10$lsc) for ____hardfp_log10$lsc
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__hardfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to _rserrno.o(.text) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    log.o(i.__hardfp_log) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log.o(i.__hardfp_log) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to _rserrno.o(.text) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    os_cpu.o(i.OSSetPrioRdy) refers to os_cpu.o(.data) for OSRdyTbl
    os_cpu.o(i.OSDelPrioRdy) refers to os_cpu.o(.data) for OSRdyTbl
    tft_lcd.o(i.LCD_WriteRAM_Prepare) refers to tft_lcd.o(i.LCD_REG_Select) for LCD_REG_Select


==============================================================================

Removing Unused input sections from the image.

    Removing app_touch.o(.rev16_text), (4 bytes).
    Removing app_touch.o(.revsh_text), (4 bytes).
    Removing app_touch.o(i.TouchStart_Judge), (56 bytes).
    Removing app_touch.o(i.Touch_Clear), (20 bytes).
    Removing app_touch.o(i.Touch_Judge), (96 bytes).
    Removing app_touch.o(i.Touch_main), (144 bytes).
    Removing app_touch.o(.bss), (14 bytes).
    Removing app_touch.o(.data), (4 bytes).
    Removing app_led.o(.rev16_text), (4 bytes).
    Removing app_led.o(.revsh_text), (4 bytes).
    Removing user_adc.o(.rev16_text), (4 bytes).
    Removing user_adc.o(.revsh_text), (4 bytes).
    Removing user_adc.o(i.ADC_DMA_Init), (148 bytes).
    Removing user_adc.o(i.Get_ACVol), (236 bytes).
    Removing user_adc.o(i.Get_DCVol), (220 bytes).
    Removing user_adc.o(i.Stop_ADC_Sampling), (16 bytes).
    Removing user.o(.rev16_text), (4 bytes).
    Removing user.o(.revsh_text), (4 bytes).
    Removing user.o(i.ADS1256_ReadVol), (120 bytes).
    Removing user.o(i.DAC_setval), (140 bytes).
    Removing user.o(i.Disp_Main), (336 bytes).
    Removing user.o(i.Get_FreSpectrum), (444 bytes).
    Removing user.o(i.User_Abs), (26 bytes).
    Removing user.o(i.User_Data_MapToRow), (118 bytes).
    Removing user.o(i.User_Data_PlotAxis), (656 bytes).
    Removing user.o(i.User_Data_PlotAxisSimple), (560 bytes).
    Removing user.o(i.User_Data_PlotClear), (74 bytes).
    Removing user.o(i.User_Data_PlotDrawScaled), (504 bytes).
    Removing user.o(i.User_FixPhase), (76 bytes).
    Removing user.o(i.User_GetSignalInf), (372 bytes).
    Removing user.o(i.dds_show), (792 bytes).
    Removing user.o(i.linear), (28 bytes).
    Removing user.o(i.meanFilter), (348 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing os_cpu.o(.rev16_text), (4 bytes).
    Removing os_cpu.o(.revsh_text), (4 bytes).
    Removing os_cpu.o(i.OSTaskRecovery), (116 bytes).
    Removing os_cpu.o(i.OSTaskSuspend), (104 bytes).
    Removing os_ui.o(.rev16_text), (4 bytes).
    Removing os_ui.o(.revsh_text), (4 bytes).
    Removing os_ui.o(i.OS_BackColor_Set), (16 bytes).
    Removing os_ui.o(i.OS_Circle_Draw), (80 bytes).
    Removing os_ui.o(i.OS_LCD_Clear), (14 bytes).
    Removing os_ui.o(i.OS_Picture_Draw), (1248 bytes).
    Removing os_ui.o(i.OS_TextColor_Set), (16 bytes).
    Removing os_ui.o(i.OS_Wave_Draw), (36 bytes).
    Removing os_ui.o(i.OS_Wave_Line_Show), (3076 bytes).
    Removing os_ui.o(i.OS_Wave_Unite), (40 bytes).
    Removing os_ui.o(i.OS_Wave_Windows_Show), (556 bytes).
    Removing os_ui.o(.data), (1 bytes).
    Removing os_malloc.o(.rev16_text), (4 bytes).
    Removing os_malloc.o(.revsh_text), (4 bytes).
    Removing os_malloc.o(i.my_mem_free), (108 bytes).
    Removing os_malloc.o(i.my_mem_init), (68 bytes).
    Removing os_malloc.o(i.my_mem_malloc), (168 bytes).
    Removing os_malloc.o(i.my_mem_perused), (64 bytes).
    Removing os_malloc.o(i.myfree), (52 bytes).
    Removing os_malloc.o(i.mymalloc), (56 bytes).
    Removing os_malloc.o(i.mymemcpy), (26 bytes).
    Removing os_malloc.o(i.mymemset), (20 bytes).
    Removing os_malloc.o(i.myrealloc), (80 bytes).
    Removing os_malloc.o(.bss), (34816 bytes).
    Removing os_malloc.o(.constdata), (36 bytes).
    Removing os_malloc.o(.data), (36 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(i.uart_init), (172 bytes).
    Removing drive_gpio.o(.rev16_text), (4 bytes).
    Removing drive_gpio.o(.revsh_text), (4 bytes).
    Removing drive_gpio.o(i.GPIO_Data_Init), (44 bytes).
    Removing drive_gpio.o(i.GPIO_Key_Init), (64 bytes).
    Removing drive_gpio.o(i.GPIO_POW_Init), (60 bytes).
    Removing drive_dma.o(.rev16_text), (4 bytes).
    Removing drive_dma.o(.revsh_text), (4 bytes).
    Removing drive_dma.o(i.ADC1_DMA2_Reload), (376 bytes).
    Removing drive_dma.o(i.ADC3_DMA2_Init), (308 bytes).
    Removing drive_dma.o(i.ADC3_DMA2_Reload), (328 bytes).
    Removing drive_dma.o(i.DAC1_DMA1_Init), (236 bytes).
    Removing drive_dma.o(i.DAC1_DMA1_Reload), (100 bytes).
    Removing drive_dma.o(.bss), (16096 bytes).
    Removing drive_dma.o(.data), (2 bytes).
    Removing drive_timer.o(.rev16_text), (4 bytes).
    Removing drive_timer.o(.revsh_text), (4 bytes).
    Removing drive_timer.o(i.TIM10_Init), (96 bytes).
    Removing drive_timer.o(i.TIM11_Init), (96 bytes).
    Removing drive_timer.o(i.TIM12_Init), (96 bytes).
    Removing drive_timer.o(i.TIM13_Init), (96 bytes).
    Removing drive_timer.o(i.TIM14_Init), (96 bytes).
    Removing drive_timer.o(i.TIM1_Init), (116 bytes).
    Removing drive_timer.o(i.TIM2_Init), (192 bytes).
    Removing drive_timer.o(i.TIM3_Init), (88 bytes).
    Removing drive_timer.o(i.TIM4_Init), (96 bytes).
    Removing drive_timer.o(i.TIM5_Init), (192 bytes).
    Removing drive_timer.o(i.TIM6_Init), (96 bytes).
    Removing drive_timer.o(i.TIM7_Init), (96 bytes).
    Removing drive_timer.o(i.TIM8_Init), (152 bytes).
    Removing drive_timer.o(i.TIM9_Init), (92 bytes).
    Removing drive_touch.o(.rev16_text), (4 bytes).
    Removing drive_touch.o(.revsh_text), (4 bytes).
    Removing drive_touch.o(i.ADS_Read_AD), (152 bytes).
    Removing drive_touch.o(i.ADS_Read_XY), (124 bytes).
    Removing drive_touch.o(i.ADS_Write_Byte), (84 bytes).
    Removing drive_touch.o(i.Delay), (16 bytes).
    Removing drive_touch.o(i.GPIO_Configuration), (164 bytes).
    Removing drive_touch.o(i.TouchRead), (172 bytes).
    Removing drive_touch.o(i.Touch_Init), (8 bytes).
    Removing drive_touchkey.o(.rev16_text), (4 bytes).
    Removing drive_touchkey.o(.revsh_text), (4 bytes).
    Removing drive_touchkey.o(i.Clear_Show), (80 bytes).
    Removing drive_touchkey.o(i.Interface), (868 bytes).
    Removing drive_touchkey.o(i.TouchKey_Draw), (684 bytes).
    Removing drive_touchkey.o(i.TouchKey_Scan), (1372 bytes).
    Removing drive_touchkey.o(.bss), (40 bytes).
    Removing drive_touchkey.o(.data), (11 bytes).
    Removing drive_pwm.o(.rev16_text), (4 bytes).
    Removing drive_pwm.o(.revsh_text), (4 bytes).
    Removing drive_pwm.o(i.PWM1_CCR_Set), (68 bytes).
    Removing drive_pwm.o(i.PWM1_Init), (240 bytes).
    Removing drive_pwm.o(i.PWM2_Init), (192 bytes).
    Removing user_spi.o(.rev16_text), (4 bytes).
    Removing user_spi.o(.revsh_text), (4 bytes).
    Removing user_spi.o(i.User_SPI_Init), (96 bytes).
    Removing user_bgd.o(.rev16_text), (4 bytes).
    Removing user_bgd.o(.revsh_text), (4 bytes).
    Removing user_bgd.o(i.Check_BGD), (456 bytes).
    Removing user_bgd.o(i.GetSum), (44 bytes).
    Removing user_bgd.o(i.Grad_Descent), (948 bytes).
    Removing user_bgd.o(.constdata), (280 bytes).
    Removing drive_ps2.o(.rev16_text), (4 bytes).
    Removing drive_ps2.o(.revsh_text), (4 bytes).
    Removing user_dac8562.o(.rev16_text), (4 bytes).
    Removing user_dac8562.o(.revsh_text), (4 bytes).
    Removing user_dac8562.o(i.DAC8562_GPIOInit), (56 bytes).
    Removing user_dac8562.o(i.DAC8562_Init), (100 bytes).
    Removing user_dac8562.o(i.DAC8562_OutAC), (28 bytes).
    Removing user_dac8562.o(i.DAC8562_OutDC), (104 bytes).
    Removing user_dac8562.o(i.Set_ACData), (320 bytes).
    Removing user_dac8562.o(.bss), (128 bytes).
    Removing user_dac8562.o(.data), (12 bytes).
    Removing user_ad8370.o(.rev16_text), (4 bytes).
    Removing user_ad8370.o(.revsh_text), (4 bytes).
    Removing user_ad8370.o(i.AD8370_GPIOInit), (56 bytes).
    Removing user_ad8370.o(i.AD8370_Init), (18 bytes).
    Removing user_ad8370.o(i.AD8370_SetTimes), (256 bytes).
    Removing user_iic.o(.rev16_text), (4 bytes).
    Removing user_iic.o(.revsh_text), (4 bytes).
    Removing user_iic.o(i.IIC_Init), (68 bytes).
    Removing user_iic.o(i.IIC_RecData), (2 bytes).
    Removing user_iic.o(i.IIC_SendData), (132 bytes).
    Removing user_iic.o(i.IIC_Start), (52 bytes).
    Removing user_iic.o(i.IIC_Stop), (64 bytes).
    Removing drive_communication.o(.rev16_text), (4 bytes).
    Removing drive_communication.o(.revsh_text), (4 bytes).
    Removing drive_communication.o(i.Init_Uart_6), (140 bytes).
    Removing drive_ads1256.o(.rev16_text), (4 bytes).
    Removing drive_ads1256.o(.revsh_text), (4 bytes).
    Removing drive_ads1256.o(i.ADS1256RREG), (80 bytes).
    Removing drive_fft.o(.rev16_text), (4 bytes).
    Removing drive_fft.o(.revsh_text), (4 bytes).
    Removing drive_fft.o(i.FFT_Harmonic), (160 bytes).
    Removing drive_fft.o(i.Wn_i), (268 bytes).
    Removing drive_fft.o(i.c_abs), (114 bytes).
    Removing drive_fft.o(i.c_div), (114 bytes).
    Removing drive_fft.o(i.c_mul), (66 bytes).
    Removing drive_fft.o(i.c_plus), (42 bytes).
    Removing drive_fft.o(i.c_sub), (42 bytes).
    Removing drive_fft.o(i.complex_abs_float), (82 bytes).
    Removing drive_fft.o(i.conjugate_complex), (54 bytes).
    Removing drive_fft.o(i.fft), (428 bytes).
    Removing drive_fft.o(i.fft_process), (144 bytes).
    Removing drive_fft.o(i.ifft), (104 bytes).
    Removing drive_fft.o(.bss), (20 bytes).
    Removing user_pga2310.o(.rev16_text), (4 bytes).
    Removing user_pga2310.o(.revsh_text), (4 bytes).
    Removing user_dac.o(.rev16_text), (4 bytes).
    Removing user_dac.o(.revsh_text), (4 bytes).
    Removing user_dac.o(i.DAC1_Vol_Set), (160 bytes).
    Removing user_dac.o(i.Set_WaveData), (468 bytes).
    Removing user_dac.o(i.User_DAC_TIM_NVIC_Init), (40 bytes).
    Removing user_dac.o(.data), (8 bytes).
    Removing drive_dac.o(.rev16_text), (4 bytes).
    Removing drive_dac.o(.revsh_text), (4 bytes).
    Removing drive_dac.o(i.DAC1_Init), (96 bytes).
    Removing drive_dac.o(i.dacClose), (24 bytes).
    Removing drive_dac.o(i.dacInit), (224 bytes).
    Removing drive_dac.o(i.dacOpen), (24 bytes).
    Removing drive_dac.o(i.ddsDataInit), (72 bytes).
    Removing drive_dac.o(i.ddsSawtoothWave), (148 bytes).
    Removing drive_dac.o(i.ddsSinWave), (100 bytes).
    Removing drive_dac.o(i.ddsSquareWave), (128 bytes).
    Removing drive_dac.o(i.ddsTriangleWave), (194 bytes).
    Removing drive_dac.o(i.setDDS), (580 bytes).
    Removing drive_dac.o(i.timer6Init), (72 bytes).
    Removing drive_dac.o(.bss), (2020 bytes).
    Removing drive_dac.o(.constdata), (8 bytes).
    Removing drive_dac.o(.data), (4 bytes).
    Removing drive_flash.o(.rev16_text), (4 bytes).
    Removing drive_flash.o(.revsh_text), (4 bytes).
    Removing drive_flash.o(i.InFLASH_GetFlashSector), (160 bytes).
    Removing drive_flash.o(i.InFLASH_Read), (32 bytes).
    Removing drive_flash.o(i.InFLASH_ReadWord), (6 bytes).
    Removing drive_flash.o(i.InFLASH_Write), (156 bytes).
    Removing character.o(.rev16_text), (4 bytes).
    Removing character.o(.revsh_text), (4 bytes).
    Removing character.o(i.LCD_GB1616), (192 bytes).
    Removing character.o(i.LCD_GB3232), (200 bytes).
    Removing character.o(i.LCD_GB4848), (200 bytes).
    Removing character.o(i.Show_Str32), (112 bytes).
    Removing character.o(i.Show_Str48), (112 bytes).
    Removing character.o(.constdata), (6242 bytes).
    Removing fontupd.o(.rev16_text), (4 bytes).
    Removing fontupd.o(.revsh_text), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing text.o(.rev16_text), (4 bytes).
    Removing text.o(.revsh_text), (4 bytes).
    Removing text.o(i.Get_HzMat), (224 bytes).
    Removing text.o(i.Show_Font), (186 bytes).
    Removing tft_lcd.o(.rev16_text), (4 bytes).
    Removing tft_lcd.o(.revsh_text), (4 bytes).
    Removing tft_lcd.o(i.Display_Control), (88 bytes).
    Removing tft_lcd.o(i.LCD_Display0x), (132 bytes).
    Removing tft_lcd.o(i.LCD_DisplayNum), (112 bytes).
    Removing tft_lcd.o(i.LCD_DisplayOff), (12 bytes).
    Removing tft_lcd.o(i.LCD_DisplayOn), (12 bytes).
    Removing tft_lcd.o(i.LCD_DisplayStringLine), (264 bytes).
    Removing tft_lcd.o(i.LCD_Display_FloatNum), (272 bytes).
    Removing tft_lcd.o(i.LCD_DrawCircle), (256 bytes).
    Removing tft_lcd.o(i.LCD_DrawCircleS), (56 bytes).
    Removing tft_lcd.o(i.LCD_DrawPoint), (82 bytes).
    Removing tft_lcd.o(i.LCD_DrawPoint_4), (162 bytes).
    Removing tft_lcd.o(i.LCD_GetColors), (24 bytes).
    Removing tft_lcd.o(i.LCD_GetFont), (12 bytes).
    Removing tft_lcd.o(i.LCD_ReadReg), (12 bytes).
    Removing tft_lcd.o(i.LCD_SetBackColor), (16 bytes).
    Removing tft_lcd.o(i.LCD_SetColors), (28 bytes).
    Removing tft_lcd.o(i.LCD_SetFont), (16 bytes).
    Removing tft_lcd.o(i.LCD_SetTextColor), (16 bytes).
    Removing tft_lcd.o(i.LCD_ShowChar), (276 bytes).
    Removing tft_lcd.o(i.LCD_WeBMP_SIZE), (30 bytes).
    Removing tft_lcd.o(i.LCD_WriteReg), (10 bytes).
    Removing tft_lcd.o(i.TFT_DispChar), (492 bytes).
    Removing w25q64.o(.rev16_text), (4 bytes).
    Removing w25q64.o(.revsh_text), (4 bytes).
    Removing w25q64.o(i.W25Q64_Erase_Chip), (48 bytes).
    Removing w25q64.o(i.W25Q64_Erase_Sector), (72 bytes).
    Removing w25q64.o(i.W25Q64_PowerDown), (40 bytes).
    Removing w25q64.o(i.W25Q64_ReadSR), (48 bytes).
    Removing w25q64.o(i.W25Q64_WAKEUP), (40 bytes).
    Removing w25q64.o(i.W25Q64_Wait_Busy), (18 bytes).
    Removing w25q64.o(i.W25Q64_Write), (184 bytes).
    Removing w25q64.o(i.W25Q64_Write_Disable), (36 bytes).
    Removing w25q64.o(i.W25Q64_Write_Enable), (36 bytes).
    Removing w25q64.o(i.W25Q64_Write_NoCheck), (70 bytes).
    Removing w25q64.o(i.W25Q64_Write_Page), (92 bytes).
    Removing w25q64.o(i.W25Q64_Write_SR), (44 bytes).
    Removing w25q64.o(.bss), (4096 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (38 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (32 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (44 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (16 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (48 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (68 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (100 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (10 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (108 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (108 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (108 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (136 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (48 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (80 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (80 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (312 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (96 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (232 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (64 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (44 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (60 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (400 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (86 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (32 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (80 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (408 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (176 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing arm_biquad_cascade_df1_f32.o(.rev16_text), (4 bytes).
    Removing arm_biquad_cascade_df1_f32.o(.revsh_text), (4 bytes).
    Removing arm_biquad_cascade_df1_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_biquad_cascade_df1_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix2_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix2_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix2_f32.o(.text), (760 bytes).
    Removing arm_cfft_radix2_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix2_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix2_init_f32.o(.text), (308 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.text), (486 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (32768 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).

727 unused section(s) (total 270946 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dfixull.s                       0x00000000   Number         0  dfixull.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log_x.o ABSOLUTE
    ../mathlib/log10.c                       0x00000000   Number         0  log10.o ABSOLUTE
    ../mathlib/log10.c                       0x00000000   Number         0  log10_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\CommonTables\arm_common_tables.c      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\FilteringFunctions\arm_biquad_cascade_df1_f32.c 0x00000000   Number         0  arm_biquad_cascade_df1_f32.o ABSOLUTE
    ..\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c 0x00000000   Number         0  arm_biquad_cascade_df1_init_f32.o ABSOLUTE
    ..\TransformFunctions\arm_bitreversal.c  0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix2_f32.c 0x00000000   Number         0  arm_cfft_radix2_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix2_init_f32.c 0x00000000   Number         0  arm_cfft_radix2_init_f32.o ABSOLUTE
    ..\\CommonTables\\arm_common_tables.c    0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\FilteringFunctions\\arm_biquad_cascade_df1_f32.c 0x00000000   Number         0  arm_biquad_cascade_df1_f32.o ABSOLUTE
    ..\\FilteringFunctions\\arm_biquad_cascade_df1_init_f32.c 0x00000000   Number         0  arm_biquad_cascade_df1_init_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix2_f32.c 0x00000000   Number         0  arm_cfft_radix2_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix2_init_f32.c 0x00000000   Number         0  arm_cfft_radix2_init_f32.o ABSOLUTE
    _01_App\App_LED.c                        0x00000000   Number         0  app_led.o ABSOLUTE
    _01_App\App_Touch.c                      0x00000000   Number         0  app_touch.o ABSOLUTE
    _01_App\User.c                           0x00000000   Number         0  user.o ABSOLUTE
    _01_App\\App_LED.c                       0x00000000   Number         0  app_led.o ABSOLUTE
    _01_App\\App_Touch.c                     0x00000000   Number         0  app_touch.o ABSOLUTE
    _01_App\\User.c                          0x00000000   Number         0  user.o ABSOLUTE
    _02_Core\\stm32f4xx_it.c                 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    _02_Core\\system_stm32f4xx.c             0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    _02_Core\startup_stm32f40_41xxx.s        0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    _02_Core\stm32f4xx_it.c                  0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    _02_Core\system_stm32f4xx.c              0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    _03_Drive\Drive_ADS1256.c                0x00000000   Number         0  drive_ads1256.o ABSOLUTE
    _03_Drive\Drive_Communication.c          0x00000000   Number         0  drive_communication.o ABSOLUTE
    _03_Drive\Drive_DAC.c                    0x00000000   Number         0  drive_dac.o ABSOLUTE
    _03_Drive\Drive_DMA.c                    0x00000000   Number         0  drive_dma.o ABSOLUTE
    _03_Drive\Drive_FFT.c                    0x00000000   Number         0  drive_fft.o ABSOLUTE
    _03_Drive\Drive_Flash.c                  0x00000000   Number         0  drive_flash.o ABSOLUTE
    _03_Drive\Drive_GPIO.c                   0x00000000   Number         0  drive_gpio.o ABSOLUTE
    _03_Drive\Drive_PS2.c                    0x00000000   Number         0  drive_ps2.o ABSOLUTE
    _03_Drive\Drive_PWM.c                    0x00000000   Number         0  drive_pwm.o ABSOLUTE
    _03_Drive\Drive_Timer.c                  0x00000000   Number         0  drive_timer.o ABSOLUTE
    _03_Drive\Drive_Touch.c                  0x00000000   Number         0  drive_touch.o ABSOLUTE
    _03_Drive\Drive_TouchKey.c               0x00000000   Number         0  drive_touchkey.o ABSOLUTE
    _03_Drive\User_AD8370.c                  0x00000000   Number         0  user_ad8370.o ABSOLUTE
    _03_Drive\User_ADC.c                     0x00000000   Number         0  user_adc.o ABSOLUTE
    _03_Drive\User_BGD.c                     0x00000000   Number         0  user_bgd.o ABSOLUTE
    _03_Drive\User_DAC.c                     0x00000000   Number         0  user_dac.o ABSOLUTE
    _03_Drive\User_DAC8562.c                 0x00000000   Number         0  user_dac8562.o ABSOLUTE
    _03_Drive\User_IIC.c                     0x00000000   Number         0  user_iic.o ABSOLUTE
    _03_Drive\User_PGA2310.c                 0x00000000   Number         0  user_pga2310.o ABSOLUTE
    _03_Drive\User_SPI.c                     0x00000000   Number         0  user_spi.o ABSOLUTE
    _03_Drive\\Drive_ADS1256.c               0x00000000   Number         0  drive_ads1256.o ABSOLUTE
    _03_Drive\\Drive_Communication.c         0x00000000   Number         0  drive_communication.o ABSOLUTE
    _03_Drive\\Drive_DAC.c                   0x00000000   Number         0  drive_dac.o ABSOLUTE
    _03_Drive\\Drive_DMA.c                   0x00000000   Number         0  drive_dma.o ABSOLUTE
    _03_Drive\\Drive_FFT.c                   0x00000000   Number         0  drive_fft.o ABSOLUTE
    _03_Drive\\Drive_Flash.c                 0x00000000   Number         0  drive_flash.o ABSOLUTE
    _03_Drive\\Drive_GPIO.c                  0x00000000   Number         0  drive_gpio.o ABSOLUTE
    _03_Drive\\Drive_PS2.c                   0x00000000   Number         0  drive_ps2.o ABSOLUTE
    _03_Drive\\Drive_PWM.c                   0x00000000   Number         0  drive_pwm.o ABSOLUTE
    _03_Drive\\Drive_Timer.c                 0x00000000   Number         0  drive_timer.o ABSOLUTE
    _03_Drive\\Drive_Touch.c                 0x00000000   Number         0  drive_touch.o ABSOLUTE
    _03_Drive\\Drive_TouchKey.c              0x00000000   Number         0  drive_touchkey.o ABSOLUTE
    _03_Drive\\User_AD8370.c                 0x00000000   Number         0  user_ad8370.o ABSOLUTE
    _03_Drive\\User_ADC.c                    0x00000000   Number         0  user_adc.o ABSOLUTE
    _03_Drive\\User_BGD.c                    0x00000000   Number         0  user_bgd.o ABSOLUTE
    _03_Drive\\User_DAC.c                    0x00000000   Number         0  user_dac.o ABSOLUTE
    _03_Drive\\User_DAC8562.c                0x00000000   Number         0  user_dac8562.o ABSOLUTE
    _03_Drive\\User_IIC.c                    0x00000000   Number         0  user_iic.o ABSOLUTE
    _03_Drive\\User_PGA2310.c                0x00000000   Number         0  user_pga2310.o ABSOLUTE
    _03_Drive\\User_SPI.c                    0x00000000   Number         0  user_spi.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\misc.c     0x00000000   Number         0  misc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\misc.c  0x00000000   Number         0  misc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    _05_Os\Os_UI.c                           0x00000000   Number         0  os_ui.o ABSOLUTE
    _05_Os\Os_cpu.c                          0x00000000   Number         0  os_cpu.o ABSOLUTE
    _05_Os\Os_malloc.c                       0x00000000   Number         0  os_malloc.o ABSOLUTE
    _05_Os\\Os_UI.c                          0x00000000   Number         0  os_ui.o ABSOLUTE
    _05_Os\\Os_cpu.c                         0x00000000   Number         0  os_cpu.o ABSOLUTE
    _05_Os\\Os_malloc.c                      0x00000000   Number         0  os_malloc.o ABSOLUTE
    _05_Os\core.asm                          0x00000000   Number         0  core.o ABSOLUTE
    _06_System\\delay.c                      0x00000000   Number         0  delay.o ABSOLUTE
    _06_System\\sys.c                        0x00000000   Number         0  sys.o ABSOLUTE
    _06_System\\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    _06_System\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    _06_System\sys.c                         0x00000000   Number         0  sys.o ABSOLUTE
    _06_System\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    _07_TFT_LCD\Character.c                  0x00000000   Number         0  character.o ABSOLUTE
    _07_TFT_LCD\TFT_LCD.c                    0x00000000   Number         0  tft_lcd.o ABSOLUTE
    _07_TFT_LCD\W25Q64.c                     0x00000000   Number         0  w25q64.o ABSOLUTE
    _07_TFT_LCD\\Character.c                 0x00000000   Number         0  character.o ABSOLUTE
    _07_TFT_LCD\\TFT_LCD.c                   0x00000000   Number         0  tft_lcd.o ABSOLUTE
    _07_TFT_LCD\\W25Q64.c                    0x00000000   Number         0  w25q64.o ABSOLUTE
    _07_TFT_LCD\\fontupd.c                   0x00000000   Number         0  fontupd.o ABSOLUTE
    _07_TFT_LCD\\spi.c                       0x00000000   Number         0  spi.o ABSOLUTE
    _07_TFT_LCD\\text.c                      0x00000000   Number         0  text.o ABSOLUTE
    _07_TFT_LCD\fonts.c                      0x00000000   Number         0  fonts.o ABSOLUTE
    _07_TFT_LCD\fontupd.c                    0x00000000   Number         0  fontupd.o ABSOLUTE
    _07_TFT_LCD\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    _07_TFT_LCD\text.c                       0x00000000   Number         0  text.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000202   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000208   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800020e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000017  0x08000214   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000218   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800021a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800021e   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000224   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800022e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000230   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000232   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000234   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000234   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800023a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800023a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800023e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800023e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000246   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000248   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000248   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800024c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000254   Section       60  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000254   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000290   Section        0  arm_biquad_cascade_df1_f32.o(.text)
    .text                                    0x080003ca   Section        0  arm_biquad_cascade_df1_init_f32.o(.text)
    .text                                    0x080003e0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080003e4   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x0800040c   Section        0  _printf_pad.o(.text)
    .text                                    0x0800045c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080005e4   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000648   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000696   Section        0  heapauxi.o(.text)
    .text                                    0x0800069c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800069e   Section        0  _rserrno.o(.text)
    .text                                    0x080006b4   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080006b7   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000ad4   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08000dd0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000dd1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000e00   Section        0  _sputc.o(.text)
    .text                                    0x08000e0c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000e14   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000e1c   Section      138  lludiv10.o(.text)
    .text                                    0x08000ea8   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000f28   Section        0  bigflt0.o(.text)
    .text                                    0x0800100c   Section        8  libspace.o(.text)
    .text                                    0x08001014   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800105e   Section        0  exit.o(.text)
    .text                                    0x08001070   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x080010f0   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800112e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001174   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080011d4   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800150c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x080015e8   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001612   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800163c   Section      580  btod.o(CL$$btod_mult_common)
    CODE                                     0x08001880   Section      124  core.o(CODE)
    $v0                                      0x08001880   Number         0  core.o(CODE)
    i.AD9959_senddata                        0x080018fc   Section        0  user.o(i.AD9959_senddata)
    i.ADC_Cmd                                0x08001990   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x080019a8   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_DMACmd                             0x080019d8   Section        0  stm32f4xx_adc.o(i.ADC_DMACmd)
    i.ADC_DMARequestAfterLastTransferCmd     0x080019ee   Section        0  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    i.ADC_DMA_NVIC_Init                      0x08001a04   Section        0  user_adc.o(i.ADC_DMA_NVIC_Init)
    i.ADC_DualMode_RegSimult_Init            0x08001a30   Section        0  user_adc.o(i.ADC_DualMode_RegSimult_Init)
    i.ADC_Init                               0x08001ae4   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_Mode_Independent_Init              0x08001b38   Section        0  user_adc.o(i.ADC_Mode_Independent_Init)
    i.ADC_RegularChannelConfig               0x08001bb0   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_TIM3_Init                          0x08001c68   Section        0  user_adc.o(i.ADC_TIM3_Init)
    i.ADS1256ReadData                        0x08001ccc   Section        0  drive_ads1256.o(i.ADS1256ReadData)
    i.ADS1256WREG                            0x08001d44   Section        0  drive_ads1256.o(i.ADS1256WREG)
    i.ADS1256_Init                           0x08001d94   Section        0  drive_ads1256.o(i.ADS1256_Init)
    i.BusFault_Handler                       0x08001e5c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Change_Menu                            0x08001e60   Section        0  user.o(i.Change_Menu)
    i.DAC_Cmd                                0x08001f44   Section        0  stm32f4xx_dac.o(i.DAC_Cmd)
    i.DAC_DMACmd                             0x08001f6c   Section        0  stm32f4xx_dac.o(i.DAC_DMACmd)
    i.DAC_DeInit                             0x08001f98   Section        0  stm32f4xx_dac.o(i.DAC_DeInit)
    i.DAC_Init                               0x08001fb0   Section        0  stm32f4xx_dac.o(i.DAC_Init)
    i.DAC_SetChannel1Data                    0x08001fe4   Section        0  stm32f4xx_dac.o(i.DAC_SetChannel1Data)
    i.DAC_SetChannel2Data                    0x08002004   Section        0  stm32f4xx_dac.o(i.DAC_SetChannel2Data)
    i.DAC_StructInit                         0x08002024   Section        0  stm32f4xx_dac.o(i.DAC_StructInit)
    i.DDSDataInit                            0x08002030   Section        0  drive_communication.o(i.DDSDataInit)
    i.DMA2_Stream0_IRQHandler                0x080021a8   Section        0  user_adc.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_ClearFlag                          0x08002340   Section        0  stm32f4xx_dma.o(i.DMA_ClearFlag)
    i.DMA_Cmd                                0x08002374   Section        0  stm32f4xx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x0800238c   Section        0  stm32f4xx_dma.o(i.DMA_DeInit)
    i.DMA_ITConfig                           0x080024e4   Section        0  stm32f4xx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08002520   Section        0  stm32f4xx_dma.o(i.DMA_Init)
    i.DebugMon_Handler                       0x08002578   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.EXTI1_IRQHandler                       0x0800257c   Section        0  user_adc.o(i.EXTI1_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x08002598   Section        0  stm32f4xx_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x080025a4   Section        0  stm32f4xx_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x080025bc   Section        0  stm32f4xx_exti.o(i.EXTI_Init)
    i.FSMC_NORSRAMCmd                        0x08002650   Section        0  stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd)
    i.FSMC_NORSRAMInit                       0x08002684   Section        0  stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit)
    i.GPIO_Config                            0x0800276c   Section        0  user.o(i.GPIO_Config)
    i.GPIO_Init                              0x080027a0   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08002830   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08002876   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08002888   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x0800288c   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.Get_Val                                0x08002890   Section        0  drive_ads1256.o(i.Get_Val)
    i.HardFault_Handler                      0x080028f8   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.IIR_ADC_DMA_Init                       0x080028fc   Section        0  user_adc.o(i.IIR_ADC_DMA_Init)
    i.IIR_Filter_Config                      0x08002990   Section        0  user_adc.o(i.IIR_Filter_Config)
    i.IIR_Filter_Enable                      0x080029c8   Section        0  user_adc.o(i.IIR_Filter_Enable)
    i.IIR_Filter_Reset                       0x080029d4   Section        0  user_adc.o(i.IIR_Filter_Reset)
    i.IIR_Init                               0x08002a14   Section        0  user_adc.o(i.IIR_Init)
    i.Init_ADS1256_GPIO                      0x08002a2c   Section        0  drive_ads1256.o(i.Init_ADS1256_GPIO)
    i.Init_All                               0x08002a8c   Section        0  user.o(i.Init_All)
    i.Init_Uart                              0x08002ac0   Section        0  drive_communication.o(i.Init_Uart)
    i.Key_StateSweep                         0x08002b44   Section        0  drive_ps2.o(i.Key_StateSweep)
    i.LCD_Appoint_Clear                      0x08002c04   Section        0  tft_lcd.o(i.LCD_Appoint_Clear)
    i.LCD_Clear                              0x08002c4c   Section        0  tft_lcd.o(i.LCD_Clear)
    i.LCD_CtrlLinesConfig                    0x08002c78   Section        0  tft_lcd.o(i.LCD_CtrlLinesConfig)
    i.LCD_DrawLine                           0x08002dcc   Section        0  tft_lcd.o(i.LCD_DrawLine)
    i.LCD_DrawRect                           0x08002e28   Section        0  tft_lcd.o(i.LCD_DrawRect)
    i.LCD_DrawRectS                          0x08002e6c   Section        0  tft_lcd.o(i.LCD_DrawRectS)
    i.LCD_DrawuniLine                        0x08002eac   Section        0  tft_lcd.o(i.LCD_DrawuniLine)
    i.LCD_FSMCConfig                         0x08002fca   Section        0  tft_lcd.o(i.LCD_FSMCConfig)
    i.LCD_REG_Select                         0x08003028   Section        0  tft_lcd.o(i.LCD_REG_Select)
    i.LCD_SetCursor                          0x08003030   Section        0  tft_lcd.o(i.LCD_SetCursor)
    i.LCD_SetDisplayWindow                   0x08003074   Section        0  tft_lcd.o(i.LCD_SetDisplayWindow)
    i.LCD_WriteRAM                           0x080030c0   Section        0  tft_lcd.o(i.LCD_WriteRAM)
    i.LCD_WriteRAM_Prepare                   0x080030c8   Section        0  tft_lcd.o(i.LCD_WriteRAM_Prepare)
    i.LED_Control                            0x080030d4   Section        0  app_led.o(i.LED_Control)
    i.LED_Init                               0x08003178   Section        0  drive_gpio.o(i.LED_Init)
    i.LED_main                               0x080031c8   Section        0  app_led.o(i.LED_main)
    i.MemManage_Handler                      0x080031f4   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.MenuHaddler_1                          0x080031f8   Section        0  user.o(i.MenuHaddler_1)
    i.MenuHaddler_2                          0x08003a00   Section        0  user.o(i.MenuHaddler_2)
    i.MenuHaddler_3                          0x080044e0   Section        0  user.o(i.MenuHaddler_3)
    i.MenuHaddler_4                          0x080045b4   Section        0  user.o(i.MenuHaddler_4)
    i.Moving_Average_Filter                  0x080045d4   Section        0  drive_ads1256.o(i.Moving_Average_Filter)
    i.MyPs2KeyScan                           0x0800464c   Section        0  drive_ps2.o(i.MyPs2KeyScan)
    i.My_Disp_Main                           0x0800481c   Section        0  user.o(i.My_Disp_Main)
    i.NMI_Handler                            0x08004928   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x0800492c   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x080049a4   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OSDelPrioRdy                           0x080049b8   Section        0  os_cpu.o(i.OSDelPrioRdy)
    i.OSGetHighRdy                           0x080049cc   Section        0  os_cpu.o(i.OSGetHighRdy)
    i.OSSetPrioRdy                           0x080049f4   Section        0  os_cpu.o(i.OSSetPrioRdy)
    i.OSTimeDly                              0x08004a08   Section        0  os_cpu.o(i.OSTimeDly)
    i.OS_Char_Show                           0x08004a44   Section        0  os_ui.o(i.OS_Char_Show)
    i.OS_Font_Show                           0x08004b64   Section        0  os_ui.o(i.OS_Font_Show)
    i.OS_HzMat_Get                           0x08004c2c   Section        0  os_ui.o(i.OS_HzMat_Get)
    i.OS_IDLE_Task                           0x08004d0c   Section        0  os_cpu.o(i.OS_IDLE_Task)
    i.OS_Init                                0x08004d14   Section        0  main.o(i.OS_Init)
    i.OS_LCD_Init                            0x08004d28   Section        0  os_ui.o(i.OS_LCD_Init)
    i.OS_Line_Draw                           0x08004d7c   Section        0  os_ui.o(i.OS_Line_Draw)
    i.OS_Num_Show                            0x08004e44   Section        0  os_ui.o(i.OS_Num_Show)
    i.OS_Point_Draw                          0x08004ea4   Section        0  os_ui.o(i.OS_Point_Draw)
    i.OS_Rect_Draw                           0x08004ec4   Section        0  os_ui.o(i.OS_Rect_Draw)
    i.OS_Sched                               0x08004f5c   Section        0  os_cpu.o(i.OS_Sched)
    i.OS_SchedLock                           0x08004fb4   Section        0  os_cpu.o(i.OS_SchedLock)
    i.OS_SchedUnlock                         0x08004fd0   Section        0  os_cpu.o(i.OS_SchedUnlock)
    i.OS_Start                               0x08004fec   Section        0  os_cpu.o(i.OS_Start)
    i.OS_String_Show                         0x08005054   Section        0  os_ui.o(i.OS_String_Show)
    i.PC1_EXTI_Config                        0x080050d0   Section        0  user_adc.o(i.PC1_EXTI_Config)
    i.PC1_GPIO_Config                        0x08005100   Section        0  user_adc.o(i.PC1_GPIO_Config)
    i.PC1_Init                               0x08005130   Section        0  user_adc.o(i.PC1_Init)
    i.PC1_NVIC_Config                        0x08005140   Section        0  user_adc.o(i.PC1_NVIC_Config)
    i.PGA2310_Init                           0x08005168   Section        0  user_pga2310.o(i.PGA2310_Init)
    i.PGA2310_SetAv                          0x08005184   Section        0  user_pga2310.o(i.PGA2310_SetAv)
    i.PS2_GPIO_Init                          0x08005268   Section        0  drive_ps2.o(i.PS2_GPIO_Init)
    PS2_GPIO_Init                            0x08005269   Thumb Code    44  drive_ps2.o(i.PS2_GPIO_Init)
    i.PS2_Keyboard_Init                      0x08005298   Section        0  drive_ps2.o(i.PS2_Keyboard_Init)
    i.PS2_ReadKeyCodon                       0x080052a0   Section        0  drive_ps2.o(i.PS2_ReadKeyCodon)
    i.PS2_ReadNum                            0x08005350   Section        0  user.o(i.PS2_ReadNum)
    i.PS2_SCL_Set                            0x0800550c   Section        0  drive_ps2.o(i.PS2_SCL_Set)
    PS2_SCL_Set                              0x0800550d   Thumb Code    82  drive_ps2.o(i.PS2_SCL_Set)
    i.PS2_SCL_Wait                           0x08005564   Section        0  drive_ps2.o(i.PS2_SCL_Wait)
    PS2_SCL_Wait                             0x08005565   Thumb Code   126  drive_ps2.o(i.PS2_SCL_Wait)
    i.PutPixel                               0x080055ec   Section        0  tft_lcd.o(i.PutPixel)
    i.RCC_AHB1PeriphClockCmd                 0x0800560c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_AHB3PeriphClockCmd                 0x0800562c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x0800564c   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB1PeriphResetCmd                 0x0800566c   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    i.RCC_APB2PeriphClockCmd                 0x0800568c   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x080056ac   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x080056cc   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SPI1_Init                              0x080057b4   Section        0  spi.o(i.SPI1_Init)
    i.SPI1_ReadWriteByte                     0x08005888   Section        0  spi.o(i.SPI1_ReadWriteByte)
    i.SPI1_SetSpeed                          0x080058c0   Section        0  spi.o(i.SPI1_SetSpeed)
    i.SPI_Cmd                                0x080058e8   Section        0  stm32f4xx_spi.o(i.SPI_Cmd)
    i.SPI_GPIO_Init                          0x08005900   Section        0  user_spi.o(i.SPI_GPIO_Init)
    i.SPI_I2S_GetFlagStatus                  0x08005990   Section        0  stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus)
    i.SPI_I2S_ReceiveData                    0x080059a2   Section        0  stm32f4xx_spi.o(i.SPI_I2S_ReceiveData)
    i.SPI_I2S_SendData                       0x080059a8   Section        0  stm32f4xx_spi.o(i.SPI_I2S_SendData)
    i.SPI_Init                               0x080059ac   Section        0  stm32f4xx_spi.o(i.SPI_Init)
    i.SPI_ReadByte                           0x080059e8   Section        0  drive_ads1256.o(i.SPI_ReadByte)
    i.SPI_WriteByte                          0x08005a30   Section        0  drive_ads1256.o(i.SPI_WriteByte)
    i.SVC_Handler                            0x08005a84   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SYSCFG_EXTILineConfig                  0x08005a88   Section        0  stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig)
    i.SetSysClock                            0x08005ac8   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08005ac9   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.Set_SamplingFre                        0x08005bb4   Section        0  user_adc.o(i.Set_SamplingFre)
    i.Set_TriggerFre                         0x08005c2c   Section        0  user_dac.o(i.Set_TriggerFre)
    i.Show_Val                               0x08005ca0   Section        0  user.o(i.Show_Val)
    i.Start_ADC_Sampling                     0x08005d3c   Section        0  user_adc.o(i.Start_ADC_Sampling)
    i.SysTick_CLKSourceConfig                0x08005d58   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08005d80   Section        0  os_cpu.o(i.SysTick_Handler)
    i.SystemInit                             0x08005e04   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_init                            0x08005e6c   Section        0  os_cpu.o(i.System_init)
    i.TFT_LCD_Init                           0x08005ec8   Section        0  tft_lcd.o(i.TFT_LCD_Init)
    i.TIM_Cmd                                0x080060b8   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_DMACmd                             0x080060d0   Section        0  stm32f4xx_tim.o(i.TIM_DMACmd)
    i.TIM_SelectOutputTrigger                0x080060e2   Section        0  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    i.TIM_TimeBaseInit                       0x080060f4   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.TIM_UpdateDisableConfig                0x08006178   Section        0  stm32f4xx_tim.o(i.TIM_UpdateDisableConfig)
    i.Task_Create                            0x08006190   Section        0  os_cpu.o(i.Task_Create)
    i.Task_End                               0x0800626c   Section        0  os_cpu.o(i.Task_End)
    i.USART1_IRQHandler                      0x08006270   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART6_IRQHandler                      0x080062f8   Section        0  drive_communication.o(i.USART6_IRQHandler)
    i.USART_ClearITPendingBit                0x08006320   Section        0  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x0800633e   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08006356   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08006370   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_Init                             0x080063c4   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08006498   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x080064a2   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x080064aa   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.User_ADC_GPIO_Init                     0x080064b0   Section        0  user_adc.o(i.User_ADC_GPIO_Init)
    i.User_ADC_Init                          0x080064f0   Section        0  user_adc.o(i.User_ADC_Init)
    i.User_DAC_Configure                     0x0800653c   Section        0  user_dac.o(i.User_DAC_Configure)
    i.User_DAC_DMA_Init                      0x0800656c   Section        0  user_dac.o(i.User_DAC_DMA_Init)
    i.User_DAC_GPIO_Init                     0x080065dc   Section        0  user_dac.o(i.User_DAC_GPIO_Init)
    i.User_DAC_Init                          0x0800661c   Section        0  user_dac.o(i.User_DAC_Init)
    i.User_DAC_TIM_Init                      0x08006640   Section        0  user_dac.o(i.User_DAC_TIM_Init)
    i.User_SPI_SendData                      0x080066a4   Section        0  user_spi.o(i.User_SPI_SendData)
    i.User_main                              0x0800675c   Section        0  user.o(i.User_main)
    i.W25Q64_Init                            0x080067bc   Section        0  w25q64.o(i.W25Q64_Init)
    i.W25Q64_Read                            0x08006834   Section        0  w25q64.o(i.W25Q64_Read)
    i.W25Q64_ReadID                          0x0800688c   Section        0  w25q64.o(i.W25Q64_ReadID)
    i.__ARM_fpclassify                       0x080068d8   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_log                           0x08006908   Section        0  log.o(i.__hardfp_log)
    i.__hardfp_log10                         0x08006cd0   Section        0  log10.o(i.__hardfp_log10)
    i.__hardfp_pow                           0x08006e00   Section        0  pow.o(i.__hardfp_pow)
    i.__hardfp_sqrt                          0x08007a50   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__kernel_poly                          0x08007aca   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08007bc8   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan                   0x08007bf8   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08007c0c   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08007c20   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08007c40   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08007c60   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x08007c80   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08007c8e   Section        0  usart.o(i._sys_exit)
    i.circulate_Av                           0x08007c94   Section        0  user.o(i.circulate_Av)
    i.crc_16                                 0x08007d84   Section        0  drive_communication.o(i.crc_16)
    crc_16                                   0x08007d85   Thumb Code    58  drive_communication.o(i.crc_16)
    i.delay_ms                               0x08007dc0   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08007e04   Section        0  delay.o(i.delay_us)
    i.fabs                                   0x08007e64   Section        0  fabs.o(i.fabs)
    i.font_init                              0x08007e7c   Section        0  fontupd.o(i.font_init)
    i.init_iir_filter                        0x08007ec0   Section        0  user_adc.o(i.init_iir_filter)
    i.log                                    0x08007f34   Section        0  log.o(i.log)
    i.main                                   0x08007f44   Section        0  main.o(i.main)
    i.menu_show                              0x08007f88   Section        0  user.o(i.menu_show)
    i.sendData                               0x08007fc4   Section        0  drive_communication.o(i.sendData)
    i.set_sweep                              0x08008150   Section        0  user.o(i.set_sweep)
    i.sqrt                                   0x080082a4   Section        0  sqrt.o(i.sqrt)
    i.usartSendData                          0x08008314   Section        0  drive_communication.o(i.usartSendData)
    usartSendData                            0x08008315   Thumb Code    42  drive_communication.o(i.usartSendData)
    locale$$code                             0x08008344   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$basic                              0x08008370   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08008370   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x08008388   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08008388   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x080083ec   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080083ec   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080083fd   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x0800853c   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x0800853c   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x0800854c   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800854c   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08008564   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08008564   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800856b   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x08008814   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x08008814   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dflt                               0x0800888c   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x0800888c   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x080088ba   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x080088ba   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x080088e0   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x080088e0   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08008958   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08008958   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08008aac   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08008aac   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08008b48   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08008b48   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08008b54   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08008b54   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08008bc0   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08008bc0   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08008bd8   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08008bd8   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08008d70   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08008d70   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08008d81   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08008f44   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08008f44   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08008f9a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08008f9a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08009026   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08009026   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08009030   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08009030   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800903a   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800903a   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0800903e   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0800903e   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08009042   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08009042   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080090a6   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x080090a6   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08009102   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x08009102   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08009132   Section    41740  fonts.o(.constdata)
    x$fpl$usenofp                            0x08009132   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0801343e   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0801343e   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08013450   Section      136  pow.o(.constdata)
    bp                                       0x08013450   Data          16  pow.o(.constdata)
    dp_h                                     0x08013460   Data          16  pow.o(.constdata)
    dp_l                                     0x08013470   Data          16  pow.o(.constdata)
    L                                        0x08013480   Data          48  pow.o(.constdata)
    P                                        0x080134b0   Data          40  pow.o(.constdata)
    .constdata                               0x080134d8   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080134d8   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080134eb   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08013500   Section       56  log.o(.constdata)
    Lg2                                      0x08013500   Data          24  log.o(.constdata)
    Lg                                       0x08013518   Data          32  log.o(.constdata)
    .constdata                               0x08013538   Section        8  qnan.o(.constdata)
    .constdata                               0x08013540   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08013540   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0801357c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080135f4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080135f8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08013600   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0801360c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0801360e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0801360f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08013610   Data           0  lc_numeric_c.o(locale$$data)
    .ARM.__AT_0x10000000                     0x10000000   Section    61440  os_malloc.o(.ARM.__AT_0x10000000)
    .ARM.__AT_0x1000F000                     0x1000f000   Section     3840  os_malloc.o(.ARM.__AT_0x1000F000)
    .data                                    0x20000000   Section        1  app_led.o(.data)
    _led_cnt                                 0x20000000   Data           1  app_led.o(.data)
    .data                                    0x20000008   Section       68  user_adc.o(.data)
    iir_b0                                   0x2000000c   Data           4  user_adc.o(.data)
    iir_b1                                   0x20000010   Data           4  user_adc.o(.data)
    iir_b2                                   0x20000014   Data           4  user_adc.o(.data)
    iir_a1                                   0x20000018   Data           4  user_adc.o(.data)
    iir_a2                                   0x2000001c   Data           4  user_adc.o(.data)
    iir_x_hist                               0x20000020   Data           8  user_adc.o(.data)
    iir_y_hist                               0x20000028   Data           8  user_adc.o(.data)
    iir_filter_enabled                       0x20000030   Data           1  user_adc.o(.data)
    iir_process_count                        0x20000034   Data           4  user_adc.o(.data)
    .data                                    0x2000004c   Section       38  user.o(.data)
    gear_change_counter                      0x2000004c   Data           1  user.o(.data)
    .data                                    0x20000074   Section       30  os_cpu.o(.data)
    .data                                    0x20000094   Section        6  usart.o(.data)
    .data                                    0x2000009c   Section        5  drive_ps2.o(.data)
    .data                                    0x200000a4   Section       32  fonts.o(.data)
    .data                                    0x200000c4   Section        8  tft_lcd.o(.data)
    LCD_Currentfonts                         0x200000c4   Data           4  tft_lcd.o(.data)
    .data                                    0x200000cc   Section        2  w25q64.o(.data)
    .data                                    0x200000ce   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x200000ce   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x200000e0   Section       20  system_stm32f4xx.o(.data)
    .bss                                     0x200000f4   Section     4560  user_adc.o(.bss)
    .bss                                     0x200012c4   Section     2400  user.o(.bss)
    .bss                                     0x20001c24   Section    39328  main.o(.bss)
    .bss                                     0x2000b5c4   Section     1152  os_cpu.o(.bss)
    .bss                                     0x2000ba44   Section      200  usart.o(.bss)
    .bss                                     0x2000bb0c   Section       10  drive_ps2.o(.bss)
    .bss                                     0x2000bb18   Section     1008  drive_communication.o(.bss)
    .bss                                     0x2000bf08   Section      256  user_dac.o(.bss)
    .bss                                     0x2000c008   Section       33  fontupd.o(.bss)
    .bss                                     0x2000c02c   Section       96  libspace.o(.bss)
    HEAP                                     0x2000c090   Section        0  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x2000c090   Section    32768  startup_stm32f40_41xxx.o(STACK)
    Heap_Mem                                 0x2000c090   Data           0  startup_stm32f40_41xxx.o(HEAP)
    Stack_Mem                                0x2000c090   Data       32768  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20014090   Data           0  startup_stm32f40_41xxx.o(STACK)
    .ARM.__AT_0x68000000                     0x68000000   Section    983040  os_malloc.o(.ARM.__AT_0x68000000)
    .ARM.__AT_0x680F0000                     0x680f0000   Section    61440  os_malloc.o(.ARM.__AT_0x680F0000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_e                                0x08000203   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000209   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800020f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_percent_end                      0x08000215   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000219   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800021b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000231   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000235   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000235   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000247   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800024d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000255   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000271   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    arm_biquad_cascade_df1_f32               0x08000291   Thumb Code   314  arm_biquad_cascade_df1_f32.o(.text)
    arm_biquad_cascade_df1_init_f32          0x080003cb   Thumb Code    22  arm_biquad_cascade_df1_init_f32.o(.text)
    __use_no_semihosting                     0x080003e1   Thumb Code     2  use_no_semi_2.o(.text)
    __2sprintf                               0x080003e5   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x0800040d   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000439   Thumb Code    34  _printf_pad.o(.text)
    __printf                                 0x0800045d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memcpy4                          0x080005e5   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080005e5   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080005e5   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800062d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x08000649   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000649   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000649   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800064d   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000697   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000699   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800069b   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800069d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800069d   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x0800069f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080006a9   Thumb Code    12  _rserrno.o(.text)
    __lib_sel_fp_printf                      0x080006b5   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000867   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08000ad5   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_char_common                      0x08000ddb   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000e01   Thumb Code    10  _sputc.o(.text)
    __rt_locale                              0x08000e0d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000e15   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000e15   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000e15   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000e1d   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000ea9   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000f29   Thumb Code   224  bigflt0.o(.text)
    __user_libspace                          0x0800100d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800100d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800100d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001015   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800105f   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08001071   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x080010f1   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800112f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001175   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080011d5   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800150d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x080015e9   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001613   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800163d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    OS_CPU_SR_Save                           0x08001881   Thumb Code     0  core.o(CODE)
    OS_CPU_SR_Restore                        0x08001889   Thumb Code     0  core.o(CODE)
    OSCtxSw                                  0x0800188f   Thumb Code     0  core.o(CODE)
    OSStartHighRdy                           0x08001899   Thumb Code     0  core.o(CODE)
    PendSV_Handler                           0x080018b9   Thumb Code     0  core.o(CODE)
    AD9959_senddata                          0x080018fd   Thumb Code   138  user.o(i.AD9959_senddata)
    ADC_Cmd                                  0x08001991   Thumb Code    22  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x080019a9   Thumb Code    34  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_DMACmd                               0x080019d9   Thumb Code    22  stm32f4xx_adc.o(i.ADC_DMACmd)
    ADC_DMARequestAfterLastTransferCmd       0x080019ef   Thumb Code    22  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    ADC_DMA_NVIC_Init                        0x08001a05   Thumb Code    42  user_adc.o(i.ADC_DMA_NVIC_Init)
    ADC_DualMode_RegSimult_Init              0x08001a31   Thumb Code   172  user_adc.o(i.ADC_DualMode_RegSimult_Init)
    ADC_Init                                 0x08001ae5   Thumb Code    74  stm32f4xx_adc.o(i.ADC_Init)
    ADC_Mode_Independent_Init                0x08001b39   Thumb Code   116  user_adc.o(i.ADC_Mode_Independent_Init)
    ADC_RegularChannelConfig                 0x08001bb1   Thumb Code   184  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    ADC_TIM3_Init                            0x08001c69   Thumb Code    96  user_adc.o(i.ADC_TIM3_Init)
    ADS1256ReadData                          0x08001ccd   Thumb Code   114  drive_ads1256.o(i.ADS1256ReadData)
    ADS1256WREG                              0x08001d45   Thumb Code    74  drive_ads1256.o(i.ADS1256WREG)
    ADS1256_Init                             0x08001d95   Thumb Code   194  drive_ads1256.o(i.ADS1256_Init)
    BusFault_Handler                         0x08001e5d   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    Change_Menu                              0x08001e61   Thumb Code   168  user.o(i.Change_Menu)
    DAC_Cmd                                  0x08001f45   Thumb Code    34  stm32f4xx_dac.o(i.DAC_Cmd)
    DAC_DMACmd                               0x08001f6d   Thumb Code    38  stm32f4xx_dac.o(i.DAC_DMACmd)
    DAC_DeInit                               0x08001f99   Thumb Code    22  stm32f4xx_dac.o(i.DAC_DeInit)
    DAC_Init                                 0x08001fb1   Thumb Code    46  stm32f4xx_dac.o(i.DAC_Init)
    DAC_SetChannel1Data                      0x08001fe5   Thumb Code    26  stm32f4xx_dac.o(i.DAC_SetChannel1Data)
    DAC_SetChannel2Data                      0x08002005   Thumb Code    26  stm32f4xx_dac.o(i.DAC_SetChannel2Data)
    DAC_StructInit                           0x08002025   Thumb Code    12  stm32f4xx_dac.o(i.DAC_StructInit)
    DDSDataInit                              0x08002031   Thumb Code   350  drive_communication.o(i.DDSDataInit)
    DMA2_Stream0_IRQHandler                  0x080021a9   Thumb Code   334  user_adc.o(i.DMA2_Stream0_IRQHandler)
    DMA_ClearFlag                            0x08002341   Thumb Code    38  stm32f4xx_dma.o(i.DMA_ClearFlag)
    DMA_Cmd                                  0x08002375   Thumb Code    22  stm32f4xx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x0800238d   Thumb Code   324  stm32f4xx_dma.o(i.DMA_DeInit)
    DMA_ITConfig                             0x080024e5   Thumb Code    58  stm32f4xx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08002521   Thumb Code    82  stm32f4xx_dma.o(i.DMA_Init)
    DebugMon_Handler                         0x08002579   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    EXTI1_IRQHandler                         0x0800257d   Thumb Code    24  user_adc.o(i.EXTI1_IRQHandler)
    EXTI_ClearITPendingBit                   0x08002599   Thumb Code     6  stm32f4xx_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x080025a5   Thumb Code    20  stm32f4xx_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x080025bd   Thumb Code   142  stm32f4xx_exti.o(i.EXTI_Init)
    FSMC_NORSRAMCmd                          0x08002651   Thumb Code    46  stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd)
    FSMC_NORSRAMInit                         0x08002685   Thumb Code   230  stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit)
    GPIO_Config                              0x0800276d   Thumb Code    46  user.o(i.GPIO_Config)
    GPIO_Init                                0x080027a1   Thumb Code   144  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08002831   Thumb Code    70  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08002877   Thumb Code    18  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08002889   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x0800288d   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    Get_Val                                  0x08002891   Thumb Code    98  drive_ads1256.o(i.Get_Val)
    HardFault_Handler                        0x080028f9   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    IIR_ADC_DMA_Init                         0x080028fd   Thumb Code   126  user_adc.o(i.IIR_ADC_DMA_Init)
    IIR_Filter_Config                        0x08002991   Thumb Code    36  user_adc.o(i.IIR_Filter_Config)
    IIR_Filter_Enable                        0x080029c9   Thumb Code     6  user_adc.o(i.IIR_Filter_Enable)
    IIR_Filter_Reset                         0x080029d5   Thumb Code    48  user_adc.o(i.IIR_Filter_Reset)
    IIR_Init                                 0x08002a15   Thumb Code    24  user_adc.o(i.IIR_Init)
    Init_ADS1256_GPIO                        0x08002a2d   Thumb Code    92  drive_ads1256.o(i.Init_ADS1256_GPIO)
    Init_All                                 0x08002a8d   Thumb Code    52  user.o(i.Init_All)
    Init_Uart                                0x08002ac1   Thumb Code   124  drive_communication.o(i.Init_Uart)
    Key_StateSweep                           0x08002b45   Thumb Code   192  drive_ps2.o(i.Key_StateSweep)
    LCD_Appoint_Clear                        0x08002c05   Thumb Code    72  tft_lcd.o(i.LCD_Appoint_Clear)
    LCD_Clear                                0x08002c4d   Thumb Code    38  tft_lcd.o(i.LCD_Clear)
    LCD_CtrlLinesConfig                      0x08002c79   Thumb Code   322  tft_lcd.o(i.LCD_CtrlLinesConfig)
    LCD_DrawLine                             0x08002dcd   Thumb Code    86  tft_lcd.o(i.LCD_DrawLine)
    LCD_DrawRect                             0x08002e29   Thumb Code    68  tft_lcd.o(i.LCD_DrawRect)
    LCD_DrawRectS                            0x08002e6d   Thumb Code    58  tft_lcd.o(i.LCD_DrawRectS)
    LCD_DrawuniLine                          0x08002ead   Thumb Code   286  tft_lcd.o(i.LCD_DrawuniLine)
    LCD_FSMCConfig                           0x08002fcb   Thumb Code    94  tft_lcd.o(i.LCD_FSMCConfig)
    LCD_REG_Select                           0x08003029   Thumb Code     8  tft_lcd.o(i.LCD_REG_Select)
    LCD_SetCursor                            0x08003031   Thumb Code    68  tft_lcd.o(i.LCD_SetCursor)
    LCD_SetDisplayWindow                     0x08003075   Thumb Code    76  tft_lcd.o(i.LCD_SetDisplayWindow)
    LCD_WriteRAM                             0x080030c1   Thumb Code     8  tft_lcd.o(i.LCD_WriteRAM)
    LCD_WriteRAM_Prepare                     0x080030c9   Thumb Code    10  tft_lcd.o(i.LCD_WriteRAM_Prepare)
    LED_Control                              0x080030d5   Thumb Code   152  app_led.o(i.LED_Control)
    LED_Init                                 0x08003179   Thumb Code    70  drive_gpio.o(i.LED_Init)
    LED_main                                 0x080031c9   Thumb Code    40  app_led.o(i.LED_main)
    MemManage_Handler                        0x080031f5   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    MenuHaddler_1                            0x080031f9   Thumb Code  1976  user.o(i.MenuHaddler_1)
    MenuHaddler_2                            0x08003a01   Thumb Code  2710  user.o(i.MenuHaddler_2)
    MenuHaddler_3                            0x080044e1   Thumb Code   130  user.o(i.MenuHaddler_3)
    MenuHaddler_4                            0x080045b5   Thumb Code    26  user.o(i.MenuHaddler_4)
    Moving_Average_Filter                    0x080045d5   Thumb Code   118  drive_ads1256.o(i.Moving_Average_Filter)
    MyPs2KeyScan                             0x0800464d   Thumb Code   456  drive_ps2.o(i.MyPs2KeyScan)
    My_Disp_Main                             0x0800481d   Thumb Code   198  user.o(i.My_Disp_Main)
    NMI_Handler                              0x08004929   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x0800492d   Thumb Code   106  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x080049a5   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OSDelPrioRdy                             0x080049b9   Thumb Code    16  os_cpu.o(i.OSDelPrioRdy)
    OSGetHighRdy                             0x080049cd   Thumb Code    32  os_cpu.o(i.OSGetHighRdy)
    OSSetPrioRdy                             0x080049f5   Thumb Code    16  os_cpu.o(i.OSSetPrioRdy)
    OSTimeDly                                0x08004a09   Thumb Code    50  os_cpu.o(i.OSTimeDly)
    OS_Char_Show                             0x08004a45   Thumb Code   266  os_ui.o(i.OS_Char_Show)
    OS_Font_Show                             0x08004b65   Thumb Code   192  os_ui.o(i.OS_Font_Show)
    OS_HzMat_Get                             0x08004c2d   Thumb Code   218  os_ui.o(i.OS_HzMat_Get)
    OS_IDLE_Task                             0x08004d0d   Thumb Code     8  os_cpu.o(i.OS_IDLE_Task)
    OS_Init                                  0x08004d15   Thumb Code    20  main.o(i.OS_Init)
    OS_LCD_Init                              0x08004d29   Thumb Code    58  os_ui.o(i.OS_LCD_Init)
    OS_Line_Draw                             0x08004d7d   Thumb Code   198  os_ui.o(i.OS_Line_Draw)
    OS_Num_Show                              0x08004e45   Thumb Code    92  os_ui.o(i.OS_Num_Show)
    OS_Point_Draw                            0x08004ea5   Thumb Code    30  os_ui.o(i.OS_Point_Draw)
    OS_Rect_Draw                             0x08004ec5   Thumb Code   146  os_ui.o(i.OS_Rect_Draw)
    OS_Sched                                 0x08004f5d   Thumb Code    68  os_cpu.o(i.OS_Sched)
    OS_SchedLock                             0x08004fb5   Thumb Code    22  os_cpu.o(i.OS_SchedLock)
    OS_SchedUnlock                           0x08004fd1   Thumb Code    22  os_cpu.o(i.OS_SchedUnlock)
    OS_Start                                 0x08004fed   Thumb Code    66  os_cpu.o(i.OS_Start)
    OS_String_Show                           0x08005055   Thumb Code   124  os_ui.o(i.OS_String_Show)
    PC1_EXTI_Config                          0x080050d1   Thumb Code    48  user_adc.o(i.PC1_EXTI_Config)
    PC1_GPIO_Config                          0x08005101   Thumb Code    42  user_adc.o(i.PC1_GPIO_Config)
    PC1_Init                                 0x08005131   Thumb Code    16  user_adc.o(i.PC1_Init)
    PC1_NVIC_Config                          0x08005141   Thumb Code    40  user_adc.o(i.PC1_NVIC_Config)
    PGA2310_Init                             0x08005169   Thumb Code    22  user_pga2310.o(i.PGA2310_Init)
    PGA2310_SetAv                            0x08005185   Thumb Code   204  user_pga2310.o(i.PGA2310_SetAv)
    PS2_Keyboard_Init                        0x08005299   Thumb Code     8  drive_ps2.o(i.PS2_Keyboard_Init)
    PS2_ReadKeyCodon                         0x080052a1   Thumb Code   170  drive_ps2.o(i.PS2_ReadKeyCodon)
    PS2_ReadNum                              0x08005351   Thumb Code   416  user.o(i.PS2_ReadNum)
    PutPixel                                 0x080055ed   Thumb Code    28  tft_lcd.o(i.PutPixel)
    RCC_AHB1PeriphClockCmd                   0x0800560d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_AHB3PeriphClockCmd                   0x0800562d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x0800564d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB1PeriphResetCmd                   0x0800566d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    RCC_APB2PeriphClockCmd                   0x0800568d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x080056ad   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x080056cd   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SPI1_Init                                0x080057b5   Thumb Code   198  spi.o(i.SPI1_Init)
    SPI1_ReadWriteByte                       0x08005889   Thumb Code    50  spi.o(i.SPI1_ReadWriteByte)
    SPI1_SetSpeed                            0x080058c1   Thumb Code    36  spi.o(i.SPI1_SetSpeed)
    SPI_Cmd                                  0x080058e9   Thumb Code    24  stm32f4xx_spi.o(i.SPI_Cmd)
    SPI_GPIO_Init                            0x08005901   Thumb Code   138  user_spi.o(i.SPI_GPIO_Init)
    SPI_I2S_GetFlagStatus                    0x08005991   Thumb Code    18  stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus)
    SPI_I2S_ReceiveData                      0x080059a3   Thumb Code     6  stm32f4xx_spi.o(i.SPI_I2S_ReceiveData)
    SPI_I2S_SendData                         0x080059a9   Thumb Code     4  stm32f4xx_spi.o(i.SPI_I2S_SendData)
    SPI_Init                                 0x080059ad   Thumb Code    60  stm32f4xx_spi.o(i.SPI_Init)
    SPI_ReadByte                             0x080059e9   Thumb Code    68  drive_ads1256.o(i.SPI_ReadByte)
    SPI_WriteByte                            0x08005a31   Thumb Code    80  drive_ads1256.o(i.SPI_WriteByte)
    SVC_Handler                              0x08005a85   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SYSCFG_EXTILineConfig                    0x08005a89   Thumb Code    60  stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig)
    Set_SamplingFre                          0x08005bb5   Thumb Code   110  user_adc.o(i.Set_SamplingFre)
    Set_TriggerFre                           0x08005c2d   Thumb Code   106  user_dac.o(i.Set_TriggerFre)
    Show_Val                                 0x08005ca1   Thumb Code   146  user.o(i.Show_Val)
    Start_ADC_Sampling                       0x08005d3d   Thumb Code    18  user_adc.o(i.Start_ADC_Sampling)
    SysTick_CLKSourceConfig                  0x08005d59   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08005d81   Thumb Code   120  os_cpu.o(i.SysTick_Handler)
    SystemInit                               0x08005e05   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    System_init                              0x08005e6d   Thumb Code    76  os_cpu.o(i.System_init)
    TFT_LCD_Init                             0x08005ec9   Thumb Code   496  tft_lcd.o(i.TFT_LCD_Init)
    TIM_Cmd                                  0x080060b9   Thumb Code    24  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_DMACmd                               0x080060d1   Thumb Code    18  stm32f4xx_tim.o(i.TIM_DMACmd)
    TIM_SelectOutputTrigger                  0x080060e3   Thumb Code    18  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    TIM_TimeBaseInit                         0x080060f5   Thumb Code   104  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    TIM_UpdateDisableConfig                  0x08006179   Thumb Code    24  stm32f4xx_tim.o(i.TIM_UpdateDisableConfig)
    Task_Create                              0x08006191   Thumb Code   212  os_cpu.o(i.Task_Create)
    Task_End                                 0x0800626d   Thumb Code     4  os_cpu.o(i.Task_End)
    USART1_IRQHandler                        0x08006271   Thumb Code   122  usart.o(i.USART1_IRQHandler)
    USART6_IRQHandler                        0x080062f9   Thumb Code    34  drive_communication.o(i.USART6_IRQHandler)
    USART_ClearITPendingBit                  0x08006321   Thumb Code    30  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x0800633f   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08006357   Thumb Code    26  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08006371   Thumb Code    84  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_Init                               0x080063c5   Thumb Code   204  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08006499   Thumb Code    10  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x080064a3   Thumb Code     8  stm32f4xx_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x080064ab   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    User_ADC_GPIO_Init                       0x080064b1   Thumb Code    58  user_adc.o(i.User_ADC_GPIO_Init)
    User_ADC_Init                            0x080064f1   Thumb Code    70  user_adc.o(i.User_ADC_Init)
    User_DAC_Configure                       0x0800653d   Thumb Code    46  user_dac.o(i.User_DAC_Configure)
    User_DAC_DMA_Init                        0x0800656d   Thumb Code   100  user_dac.o(i.User_DAC_DMA_Init)
    User_DAC_GPIO_Init                       0x080065dd   Thumb Code    58  user_dac.o(i.User_DAC_GPIO_Init)
    User_DAC_Init                            0x0800661d   Thumb Code    34  user_dac.o(i.User_DAC_Init)
    User_DAC_TIM_Init                        0x08006641   Thumb Code    90  user_dac.o(i.User_DAC_TIM_Init)
    User_SPI_SendData                        0x080066a5   Thumb Code   180  user_spi.o(i.User_SPI_SendData)
    User_main                                0x0800675d   Thumb Code    88  user.o(i.User_main)
    W25Q64_Init                              0x080067bd   Thumb Code   106  w25q64.o(i.W25Q64_Init)
    W25Q64_Read                              0x08006835   Thumb Code    82  w25q64.o(i.W25Q64_Read)
    W25Q64_ReadID                            0x0800688d   Thumb Code    70  w25q64.o(i.W25Q64_ReadID)
    __ARM_fpclassify                         0x080068d9   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_log                             0x08006909   Thumb Code   872  log.o(i.__hardfp_log)
    __hardfp_log10                           0x08006cd1   Thumb Code   260  log10.o(i.__hardfp_log10)
    __hardfp_pow                             0x08006e01   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __hardfp_sqrt                            0x08007a51   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __kernel_poly                            0x08007acb   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08007bc9   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan                     0x08007bf9   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08007c0d   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08007c21   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08007c41   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08007c61   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x08007c81   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08007c8f   Thumb Code     6  usart.o(i._sys_exit)
    circulate_Av                             0x08007c95   Thumb Code   186  user.o(i.circulate_Av)
    delay_ms                                 0x08007dc1   Thumb Code    60  delay.o(i.delay_ms)
    delay_us                                 0x08007e05   Thumb Code    92  delay.o(i.delay_us)
    fabs                                     0x08007e65   Thumb Code    24  fabs.o(i.fabs)
    font_init                                0x08007e7d   Thumb Code    64  fontupd.o(i.font_init)
    init_iir_filter                          0x08007ec1   Thumb Code    84  user_adc.o(i.init_iir_filter)
    log                                      0x08007f35   Thumb Code    16  log.o(i.log)
    main                                     0x08007f45   Thumb Code    44  main.o(i.main)
    menu_show                                0x08007f89   Thumb Code    56  user.o(i.menu_show)
    sendData                                 0x08007fc5   Thumb Code   388  drive_communication.o(i.sendData)
    set_sweep                                0x08008151   Thumb Code   310  user.o(i.set_sweep)
    sqrt                                     0x080082a5   Thumb Code   110  sqrt.o(i.sqrt)
    _get_lc_numeric                          0x08008345   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dneg                             0x08008371   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08008371   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x08008377   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x08008377   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x0800837d   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x08008383   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x08008389   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08008389   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x080083ed   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080083ed   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x0800853d   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x0800854d   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08008565   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08008565   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x08008815   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08008815   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_i2d                              0x0800888d   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800888d   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x080088bb   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080088bb   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x080088e1   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x080088e1   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08008943   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08008959   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08008959   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08008aad   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08008b49   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08008b55   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08008b55   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08008bc1   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08008bc1   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08008bd9   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08008d71   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08008d71   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08008f45   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08008f45   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08008f9b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08009027   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800902f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800902f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08009031   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800903b   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800903f   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08009043   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080090a7   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08009103   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    Font_1608                                0x08009132   Data        6080  fonts.o(.constdata)
    __I$use$fp                               0x08009132   Number         0  usenofp.o(x$fpl$usenofp)
    Font_1608x                               0x0800a8f2   Data        6080  fonts.o(.constdata)
    Font_3216                                0x0800c0b2   Data        6080  fonts.o(.constdata)
    Font_2412                                0x0800d872   Data       12480  fonts.o(.constdata)
    asc2_1608                                0x08010932   Data        1520  fonts.o(.constdata)
    asc2_2412                                0x08010f22   Data        3420  fonts.o(.constdata)
    asc2_3216                                0x08011c7e   Data        6080  fonts.o(.constdata)
    __mathlib_zero                           0x08013538   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x080135d4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080135f4   Number         0  anon$$obj.o(Region$$Table)
    mem3base                                 0x10000000   Data       61440  os_malloc.o(.ARM.__AT_0x10000000)
    mem3mapbase                              0x1000f000   Data        3840  os_malloc.o(.ARM.__AT_0x1000F000)
    ADC_Sign                                 0x20000008   Data           1  user_adc.o(.data)
    first_flag                               0x20000038   Data           1  user_adc.o(.data)
    max_value                                0x2000003c   Data           4  user_adc.o(.data)
    min_value                                0x20000040   Data           4  user_adc.o(.data)
    change_flag                              0x20000044   Data           1  user_adc.o(.data)
    direct_val                               0x20000048   Data           4  user_adc.o(.data)
    MenuSign                                 0x2000004d   Data           1  user.o(.data)
    menu_flag                                0x2000004e   Data           1  user.o(.data)
    decrease_db                              0x20000050   Data           4  user.o(.data)
    a1                                       0x20000054   Data           4  user.o(.data)
    a2                                       0x20000058   Data           4  user.o(.data)
    b1                                       0x2000005c   Data           4  user.o(.data)
    b2                                       0x20000060   Data           4  user.o(.data)
    b3                                       0x20000064   Data           4  user.o(.data)
    direct_coef                              0x20000068   Data           1  user.o(.data)
    flite_type                               0x20000069   Data           1  user.o(.data)
    filter                                   0x2000006a   Data           8  user.o(.data)
    CPU_ExceptStkBase                        0x20000074   Data           4  os_cpu.o(.data)
    p_TCB_Cur                                0x20000078   Data           4  os_cpu.o(.data)
    p_TCBHightRdy                            0x2000007c   Data           4  os_cpu.o(.data)
    OS_PrioCur                               0x20000080   Data           1  os_cpu.o(.data)
    OS_PrioHighRdy                           0x20000081   Data           1  os_cpu.o(.data)
    OSRdyTbl                                 0x20000084   Data           4  os_cpu.o(.data)
    fac_ms                                   0x20000088   Data           4  os_cpu.o(.data)
    fac_us                                   0x2000008c   Data           4  os_cpu.o(.data)
    OS_Running                               0x20000090   Data           1  os_cpu.o(.data)
    Sched_flag                               0x20000091   Data           1  os_cpu.o(.data)
    __stdout                                 0x20000094   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000098   Data           2  usart.o(.data)
    Count                                    0x2000009c   Data           4  drive_ps2.o(.data)
    Ps2KeyValue                              0x200000a0   Data           1  drive_ps2.o(.data)
    Font16x08                                0x200000a4   Data           8  fonts.o(.data)
    Font24x12                                0x200000ac   Data           8  fonts.o(.data)
    Font16x08x                               0x200000b4   Data           8  fonts.o(.data)
    Font32x16                                0x200000bc   Data           8  fonts.o(.data)
    TextColor                                0x200000c8   Data           2  tft_lcd.o(.data)
    BackColor                                0x200000ca   Data           2  tft_lcd.o(.data)
    W25Q64_TYPE                              0x200000cc   Data           2  w25q64.o(.data)
    SystemCoreClock                          0x200000e0   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x200000e4   Data          16  system_stm32f4xx.o(.data)
    ADCData                                  0x200000f4   Data        4096  user_adc.o(.bss)
    iir_instance                             0x200010f4   Data          12  user_adc.o(.bss)
    iir_state                                0x20001100   Data          16  user_adc.o(.bss)
    iir_coeff                                0x20001110   Data          20  user_adc.o(.bss)
    sample_data                              0x20001124   Data         416  user_adc.o(.bss)
    SweepValue                               0x200012c4   Data        1200  user.o(.bss)
    AvValue                                  0x20001774   Data        1200  user.o(.bss)
    TASK_0_STK                               0x20001c24   Data       36000  main.o(.bss)
    TASK_1_STK                               0x2000a8c4   Data        2048  main.o(.bss)
    TASK_4_STK                               0x2000b0c4   Data        1024  main.o(.bss)
    TASK_5_STK                               0x2000b4c4   Data         256  main.o(.bss)
    CPU_ExceptStk                            0x2000b5c4   Data         512  os_cpu.o(.bss)
    IDLE_STK                                 0x2000b7c4   Data         256  os_cpu.o(.bss)
    TCB_Task                                 0x2000b8c4   Data         384  os_cpu.o(.bss)
    USART_RX_BUF                             0x2000ba44   Data         200  usart.o(.bss)
    Key_FSM_PS2                              0x2000bb0c   Data          10  drive_ps2.o(.bss)
    communicationData                        0x2000bb18   Data          77  drive_communication.o(.bss)
    dds                                      0x2000bb68   Data         928  drive_communication.o(.bss)
    WaveData                                 0x2000bf08   Data         256  user_dac.o(.bss)
    ftinfo                                   0x2000c008   Data          33  fontupd.o(.bss)
    __libspace_start                         0x2000c02c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000c08c   Data           0  libspace.o(.bss)
    mem2base                                 0x68000000   Data       983040  os_malloc.o(.ARM.__AT_0x68000000)
    mem2mapbase                              0x680f0000   Data       61440  os_malloc.o(.ARM.__AT_0x680F0000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00013704, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00013610, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO         5692    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5929  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6476    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         6478    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         6480    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         5922    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         5918    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         5919    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000208   0x08000208   0x00000006   Code   RO         5920    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         5921    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000214   0x08000214   0x00000004   Code   RO         6093    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000218   0x08000218   0x00000002   Code   RO         6296    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800021a   0x0800021a   0x00000004   Code   RO         6297    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6300    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6303    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6305    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6307    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000006   Code   RO         6308    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         6310    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         6312    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         6314    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x0000000a   Code   RO         6315    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6316    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6318    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6320    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6322    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6324    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6326    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6328    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6330    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6334    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6336    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6338    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6340    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000002   Code   RO         6341    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000002   Code   RO         6422    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6457    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6459    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6461    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6464    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6467    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6469    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6472    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000002   Code   RO         6473    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         6073    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000234   0x08000234   0x00000000   Code   RO         6200    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000006   Code   RO         6212    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         6202    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800023a   0x0800023a   0x00000004   Code   RO         6203    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         6205    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000008   Code   RO         6206    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000246   0x08000246   0x00000002   Code   RO         6344    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000248   0x08000248   0x00000000   Code   RO         6380    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000248   0x08000248   0x00000004   Code   RO         6381    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x00000006   Code   RO         6382    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000252   0x08000252   0x00000002   PAD
    0x08000254   0x08000254   0x0000003c   Code   RO         5693    .text               startup_stm32f40_41xxx.o
    0x08000290   0x08000290   0x0000013a   Code   RO         5699    .text               arm_cortexM4lf_math.lib(arm_biquad_cascade_df1_f32.o)
    0x080003ca   0x080003ca   0x00000016   Code   RO         5744    .text               arm_cortexM4lf_math.lib(arm_biquad_cascade_df1_init_f32.o)
    0x080003e0   0x080003e0   0x00000002   Code   RO         5879    .text               c_w.l(use_no_semi_2.o)
    0x080003e2   0x080003e2   0x00000002   PAD
    0x080003e4   0x080003e4   0x00000028   Code   RO         5889    .text               c_w.l(noretval__2sprintf.o)
    0x0800040c   0x0800040c   0x0000004e   Code   RO         5893    .text               c_w.l(_printf_pad.o)
    0x0800045a   0x0800045a   0x00000002   PAD
    0x0800045c   0x0800045c   0x00000188   Code   RO         5914    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080005e4   0x080005e4   0x00000064   Code   RO         5923    .text               c_w.l(rt_memcpy_w.o)
    0x08000648   0x08000648   0x0000004e   Code   RO         5925    .text               c_w.l(rt_memclr_w.o)
    0x08000696   0x08000696   0x00000006   Code   RO         5927    .text               c_w.l(heapauxi.o)
    0x0800069c   0x0800069c   0x00000002   Code   RO         6071    .text               c_w.l(use_no_semi.o)
    0x0800069e   0x0800069e   0x00000016   Code   RO         6078    .text               c_w.l(_rserrno.o)
    0x080006b4   0x080006b4   0x0000041e   Code   RO         6082    .text               c_w.l(_printf_fp_dec.o)
    0x08000ad2   0x08000ad2   0x00000002   PAD
    0x08000ad4   0x08000ad4   0x000002fc   Code   RO         6084    .text               c_w.l(_printf_fp_hex.o)
    0x08000dd0   0x08000dd0   0x00000030   Code   RO         6087    .text               c_w.l(_printf_char_common.o)
    0x08000e00   0x08000e00   0x0000000a   Code   RO         6089    .text               c_w.l(_sputc.o)
    0x08000e0a   0x08000e0a   0x00000002   PAD
    0x08000e0c   0x08000e0c   0x00000008   Code   RO         6219    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000e14   0x08000e14   0x00000008   Code   RO         6224    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000e1c   0x08000e1c   0x0000008a   Code   RO         6226    .text               c_w.l(lludiv10.o)
    0x08000ea6   0x08000ea6   0x00000002   PAD
    0x08000ea8   0x08000ea8   0x00000080   Code   RO         6230    .text               c_w.l(_printf_fp_infnan.o)
    0x08000f28   0x08000f28   0x000000e4   Code   RO         6236    .text               c_w.l(bigflt0.o)
    0x0800100c   0x0800100c   0x00000008   Code   RO         6274    .text               c_w.l(libspace.o)
    0x08001014   0x08001014   0x0000004a   Code   RO         6277    .text               c_w.l(sys_stackheap_outer.o)
    0x0800105e   0x0800105e   0x00000012   Code   RO         6283    .text               c_w.l(exit.o)
    0x08001070   0x08001070   0x00000080   Code   RO         6289    .text               c_w.l(strcmpv7m.o)
    0x080010f0   0x080010f0   0x0000003e   Code   RO         6239    CL$$btod_d2e        c_w.l(btod.o)
    0x0800112e   0x0800112e   0x00000046   Code   RO         6241    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001174   0x08001174   0x00000060   Code   RO         6240    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080011d4   0x080011d4   0x00000338   Code   RO         6249    CL$$btod_div_common  c_w.l(btod.o)
    0x0800150c   0x0800150c   0x000000dc   Code   RO         6246    CL$$btod_e2e        c_w.l(btod.o)
    0x080015e8   0x080015e8   0x0000002a   Code   RO         6243    CL$$btod_ediv       c_w.l(btod.o)
    0x08001612   0x08001612   0x0000002a   Code   RO         6242    CL$$btod_emul       c_w.l(btod.o)
    0x0800163c   0x0800163c   0x00000244   Code   RO         6248    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001880   0x08001880   0x0000007c   Code   RO         1055    CODE                core.o
    0x080018fc   0x080018fc   0x00000094   Code   RO          467    i.AD9959_senddata   user.o
    0x08001990   0x08001990   0x00000016   Code   RO         2760    i.ADC_Cmd           stm32f4xx_adc.o
    0x080019a6   0x080019a6   0x00000002   PAD
    0x080019a8   0x080019a8   0x00000030   Code   RO         2761    i.ADC_CommonInit    stm32f4xx_adc.o
    0x080019d8   0x080019d8   0x00000016   Code   RO         2764    i.ADC_DMACmd        stm32f4xx_adc.o
    0x080019ee   0x080019ee   0x00000016   Code   RO         2765    i.ADC_DMARequestAfterLastTransferCmd  stm32f4xx_adc.o
    0x08001a04   0x08001a04   0x0000002a   Code   RO          303    i.ADC_DMA_NVIC_Init  user_adc.o
    0x08001a2e   0x08001a2e   0x00000002   PAD
    0x08001a30   0x08001a30   0x000000b4   Code   RO          304    i.ADC_DualMode_RegSimult_Init  user_adc.o
    0x08001ae4   0x08001ae4   0x00000054   Code   RO         2780    i.ADC_Init          stm32f4xx_adc.o
    0x08001b38   0x08001b38   0x00000078   Code   RO          305    i.ADC_Mode_Independent_Init  user_adc.o
    0x08001bb0   0x08001bb0   0x000000b8   Code   RO         2785    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x08001c68   0x08001c68   0x00000064   Code   RO          306    i.ADC_TIM3_Init     user_adc.o
    0x08001ccc   0x08001ccc   0x00000078   Code   RO         1829    i.ADS1256ReadData   drive_ads1256.o
    0x08001d44   0x08001d44   0x00000050   Code   RO         1830    i.ADS1256WREG       drive_ads1256.o
    0x08001d94   0x08001d94   0x000000c8   Code   RO         1831    i.ADS1256_Init      drive_ads1256.o
    0x08001e5c   0x08001e5c   0x00000004   Code   RO         5570    i.BusFault_Handler  stm32f4xx_it.o
    0x08001e60   0x08001e60   0x000000e4   Code   RO          469    i.Change_Menu       user.o
    0x08001f44   0x08001f44   0x00000028   Code   RO         3048    i.DAC_Cmd           stm32f4xx_dac.o
    0x08001f6c   0x08001f6c   0x0000002c   Code   RO         3049    i.DAC_DMACmd        stm32f4xx_dac.o
    0x08001f98   0x08001f98   0x00000016   Code   RO         3050    i.DAC_DeInit        stm32f4xx_dac.o
    0x08001fae   0x08001fae   0x00000002   PAD
    0x08001fb0   0x08001fb0   0x00000034   Code   RO         3056    i.DAC_Init          stm32f4xx_dac.o
    0x08001fe4   0x08001fe4   0x00000020   Code   RO         3057    i.DAC_SetChannel1Data  stm32f4xx_dac.o
    0x08002004   0x08002004   0x00000020   Code   RO         3058    i.DAC_SetChannel2Data  stm32f4xx_dac.o
    0x08002024   0x08002024   0x0000000c   Code   RO         3061    i.DAC_StructInit    stm32f4xx_dac.o
    0x08002030   0x08002030   0x00000178   Code   RO         1768    i.DDSDataInit       drive_communication.o
    0x080021a8   0x080021a8   0x00000198   Code   RO          307    i.DMA2_Stream0_IRQHandler  user_adc.o
    0x08002340   0x08002340   0x00000034   Code   RO         3162    i.DMA_ClearFlag     stm32f4xx_dma.o
    0x08002374   0x08002374   0x00000016   Code   RO         3164    i.DMA_Cmd           stm32f4xx_dma.o
    0x0800238a   0x0800238a   0x00000002   PAD
    0x0800238c   0x0800238c   0x00000158   Code   RO         3165    i.DMA_DeInit        stm32f4xx_dma.o
    0x080024e4   0x080024e4   0x0000003a   Code   RO         3175    i.DMA_ITConfig      stm32f4xx_dma.o
    0x0800251e   0x0800251e   0x00000002   PAD
    0x08002520   0x08002520   0x00000058   Code   RO         3176    i.DMA_Init          stm32f4xx_dma.o
    0x08002578   0x08002578   0x00000002   Code   RO         5571    i.DebugMon_Handler  stm32f4xx_it.o
    0x0800257a   0x0800257a   0x00000002   PAD
    0x0800257c   0x0800257c   0x0000001c   Code   RO          308    i.EXTI1_IRQHandler  user_adc.o
    0x08002598   0x08002598   0x0000000c   Code   RO         3545    i.EXTI_ClearITPendingBit  stm32f4xx_exti.o
    0x080025a4   0x080025a4   0x00000018   Code   RO         3549    i.EXTI_GetITStatus  stm32f4xx_exti.o
    0x080025bc   0x080025bc   0x00000094   Code   RO         3550    i.EXTI_Init         stm32f4xx_exti.o
    0x08002650   0x08002650   0x00000034   Code   RO         3617    i.FSMC_NORSRAMCmd   stm32f4xx_fsmc.o
    0x08002684   0x08002684   0x000000e6   Code   RO         3619    i.FSMC_NORSRAMInit  stm32f4xx_fsmc.o
    0x0800276a   0x0800276a   0x00000002   PAD
    0x0800276c   0x0800276c   0x00000034   Code   RO          472    i.GPIO_Config       user.o
    0x080027a0   0x080027a0   0x00000090   Code   RO         3738    i.GPIO_Init         stm32f4xx_gpio.o
    0x08002830   0x08002830   0x00000046   Code   RO         3739    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08002876   0x08002876   0x00000012   Code   RO         3742    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x08002888   0x08002888   0x00000004   Code   RO         3745    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x0800288c   0x0800288c   0x00000004   Code   RO         3746    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08002890   0x08002890   0x00000068   Code   RO         1832    i.Get_Val           drive_ads1256.o
    0x080028f8   0x080028f8   0x00000004   Code   RO         5572    i.HardFault_Handler  stm32f4xx_it.o
    0x080028fc   0x080028fc   0x00000094   Code   RO          311    i.IIR_ADC_DMA_Init  user_adc.o
    0x08002990   0x08002990   0x00000038   Code   RO          312    i.IIR_Filter_Config  user_adc.o
    0x080029c8   0x080029c8   0x0000000c   Code   RO          313    i.IIR_Filter_Enable  user_adc.o
    0x080029d4   0x080029d4   0x00000040   Code   RO          314    i.IIR_Filter_Reset  user_adc.o
    0x08002a14   0x08002a14   0x00000018   Code   RO          315    i.IIR_Init          user_adc.o
    0x08002a2c   0x08002a2c   0x00000060   Code   RO         1833    i.Init_ADS1256_GPIO  drive_ads1256.o
    0x08002a8c   0x08002a8c   0x00000034   Code   RO          474    i.Init_All          user.o
    0x08002ac0   0x08002ac0   0x00000084   Code   RO         1769    i.Init_Uart         drive_communication.o
    0x08002b44   0x08002b44   0x000000c0   Code   RO         1575    i.Key_StateSweep    drive_ps2.o
    0x08002c04   0x08002c04   0x00000048   Code   RO         2398    i.LCD_Appoint_Clear  tft_lcd.o
    0x08002c4c   0x08002c4c   0x0000002c   Code   RO         2399    i.LCD_Clear         tft_lcd.o
    0x08002c78   0x08002c78   0x00000154   Code   RO         2400    i.LCD_CtrlLinesConfig  tft_lcd.o
    0x08002dcc   0x08002dcc   0x0000005c   Code   RO         2409    i.LCD_DrawLine      tft_lcd.o
    0x08002e28   0x08002e28   0x00000044   Code   RO         2412    i.LCD_DrawRect      tft_lcd.o
    0x08002e6c   0x08002e6c   0x00000040   Code   RO         2413    i.LCD_DrawRectS     tft_lcd.o
    0x08002eac   0x08002eac   0x0000011e   Code   RO         2414    i.LCD_DrawuniLine   tft_lcd.o
    0x08002fca   0x08002fca   0x0000005e   Code   RO         2415    i.LCD_FSMCConfig    tft_lcd.o
    0x08003028   0x08003028   0x00000008   Code   RO         2616    i.LCD_REG_Select    tft_lcd.o
    0x08003030   0x08003030   0x00000044   Code   RO         2421    i.LCD_SetCursor     tft_lcd.o
    0x08003074   0x08003074   0x0000004c   Code   RO         2422    i.LCD_SetDisplayWindow  tft_lcd.o
    0x080030c0   0x080030c0   0x00000008   Code   RO         2611    i.LCD_WriteRAM      tft_lcd.o
    0x080030c8   0x080030c8   0x0000000a   Code   RO         2621    i.LCD_WriteRAM_Prepare  tft_lcd.o
    0x080030d2   0x080030d2   0x00000002   PAD
    0x080030d4   0x080030d4   0x000000a4   Code   RO          268    i.LED_Control       app_led.o
    0x08003178   0x08003178   0x00000050   Code   RO         1180    i.LED_Init          drive_gpio.o
    0x080031c8   0x080031c8   0x0000002c   Code   RO          269    i.LED_main          app_led.o
    0x080031f4   0x080031f4   0x00000004   Code   RO         5573    i.MemManage_Handler  stm32f4xx_it.o
    0x080031f8   0x080031f8   0x00000808   Code   RO          475    i.MenuHaddler_1     user.o
    0x08003a00   0x08003a00   0x00000ae0   Code   RO          476    i.MenuHaddler_2     user.o
    0x080044e0   0x080044e0   0x000000d4   Code   RO          477    i.MenuHaddler_3     user.o
    0x080045b4   0x080045b4   0x00000020   Code   RO          478    i.MenuHaddler_4     user.o
    0x080045d4   0x080045d4   0x00000076   Code   RO         1834    i.Moving_Average_Filter  drive_ads1256.o
    0x0800464a   0x0800464a   0x00000002   PAD
    0x0800464c   0x0800464c   0x000001d0   Code   RO         1576    i.MyPs2KeyScan      drive_ps2.o
    0x0800481c   0x0800481c   0x0000010c   Code   RO          479    i.My_Disp_Main      user.o
    0x08004928   0x08004928   0x00000002   Code   RO         5574    i.NMI_Handler       stm32f4xx_it.o
    0x0800492a   0x0800492a   0x00000002   PAD
    0x0800492c   0x0800492c   0x00000078   Code   RO         5143    i.NVIC_Init         misc.o
    0x080049a4   0x080049a4   0x00000014   Code   RO         5144    i.NVIC_PriorityGroupConfig  misc.o
    0x080049b8   0x080049b8   0x00000014   Code   RO          793    i.OSDelPrioRdy      os_cpu.o
    0x080049cc   0x080049cc   0x00000028   Code   RO          697    i.OSGetHighRdy      os_cpu.o
    0x080049f4   0x080049f4   0x00000014   Code   RO          788    i.OSSetPrioRdy      os_cpu.o
    0x08004a08   0x08004a08   0x0000003c   Code   RO          700    i.OSTimeDly         os_cpu.o
    0x08004a44   0x08004a44   0x00000120   Code   RO          843    i.OS_Char_Show      os_ui.o
    0x08004b64   0x08004b64   0x000000c8   Code   RO          845    i.OS_Font_Show      os_ui.o
    0x08004c2c   0x08004c2c   0x000000e0   Code   RO          846    i.OS_HzMat_Get      os_ui.o
    0x08004d0c   0x08004d0c   0x00000008   Code   RO          701    i.OS_IDLE_Task      os_cpu.o
    0x08004d14   0x08004d14   0x00000014   Code   RO          668    i.OS_Init           main.o
    0x08004d28   0x08004d28   0x00000054   Code   RO          848    i.OS_LCD_Init       os_ui.o
    0x08004d7c   0x08004d7c   0x000000c6   Code   RO          849    i.OS_Line_Draw      os_ui.o
    0x08004e42   0x08004e42   0x00000002   PAD
    0x08004e44   0x08004e44   0x00000060   Code   RO          850    i.OS_Num_Show       os_ui.o
    0x08004ea4   0x08004ea4   0x0000001e   Code   RO          852    i.OS_Point_Draw     os_ui.o
    0x08004ec2   0x08004ec2   0x00000002   PAD
    0x08004ec4   0x08004ec4   0x00000098   Code   RO          853    i.OS_Rect_Draw      os_ui.o
    0x08004f5c   0x08004f5c   0x00000058   Code   RO          702    i.OS_Sched          os_cpu.o
    0x08004fb4   0x08004fb4   0x0000001c   Code   RO          703    i.OS_SchedLock      os_cpu.o
    0x08004fd0   0x08004fd0   0x0000001c   Code   RO          704    i.OS_SchedUnlock    os_cpu.o
    0x08004fec   0x08004fec   0x00000068   Code   RO          705    i.OS_Start          os_cpu.o
    0x08005054   0x08005054   0x0000007c   Code   RO          854    i.OS_String_Show    os_ui.o
    0x080050d0   0x080050d0   0x00000030   Code   RO          316    i.PC1_EXTI_Config   user_adc.o
    0x08005100   0x08005100   0x00000030   Code   RO          317    i.PC1_GPIO_Config   user_adc.o
    0x08005130   0x08005130   0x00000010   Code   RO          318    i.PC1_Init          user_adc.o
    0x08005140   0x08005140   0x00000028   Code   RO          319    i.PC1_NVIC_Config   user_adc.o
    0x08005168   0x08005168   0x0000001c   Code   RO         1991    i.PGA2310_Init      user_pga2310.o
    0x08005184   0x08005184   0x000000e4   Code   RO         1992    i.PGA2310_SetAv     user_pga2310.o
    0x08005268   0x08005268   0x00000030   Code   RO         1577    i.PS2_GPIO_Init     drive_ps2.o
    0x08005298   0x08005298   0x00000008   Code   RO         1578    i.PS2_Keyboard_Init  drive_ps2.o
    0x080052a0   0x080052a0   0x000000b0   Code   RO         1579    i.PS2_ReadKeyCodon  drive_ps2.o
    0x08005350   0x08005350   0x000001bc   Code   RO          480    i.PS2_ReadNum       user.o
    0x0800550c   0x0800550c   0x00000058   Code   RO         1580    i.PS2_SCL_Set       drive_ps2.o
    0x08005564   0x08005564   0x00000088   Code   RO         1581    i.PS2_SCL_Wait      drive_ps2.o
    0x080055ec   0x080055ec   0x00000020   Code   RO         2428    i.PutPixel          tft_lcd.o
    0x0800560c   0x0800560c   0x00000020   Code   RO         4059    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x0800562c   0x0800562c   0x00000020   Code   RO         4065    i.RCC_AHB3PeriphClockCmd  stm32f4xx_rcc.o
    0x0800564c   0x0800564c   0x00000020   Code   RO         4068    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x0800566c   0x0800566c   0x00000020   Code   RO         4070    i.RCC_APB1PeriphResetCmd  stm32f4xx_rcc.o
    0x0800568c   0x0800568c   0x00000020   Code   RO         4071    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080056ac   0x080056ac   0x00000020   Code   RO         4073    i.RCC_APB2PeriphResetCmd  stm32f4xx_rcc.o
    0x080056cc   0x080056cc   0x000000e8   Code   RO         4080    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x080057b4   0x080057b4   0x000000d4   Code   RO         2329    i.SPI1_Init         spi.o
    0x08005888   0x08005888   0x00000038   Code   RO         2330    i.SPI1_ReadWriteByte  spi.o
    0x080058c0   0x080058c0   0x00000028   Code   RO         2331    i.SPI1_SetSpeed     spi.o
    0x080058e8   0x080058e8   0x00000018   Code   RO         5193    i.SPI_Cmd           stm32f4xx_spi.o
    0x08005900   0x08005900   0x00000090   Code   RO         1504    i.SPI_GPIO_Init     user_spi.o
    0x08005990   0x08005990   0x00000012   Code   RO         5201    i.SPI_I2S_GetFlagStatus  stm32f4xx_spi.o
    0x080059a2   0x080059a2   0x00000006   Code   RO         5204    i.SPI_I2S_ReceiveData  stm32f4xx_spi.o
    0x080059a8   0x080059a8   0x00000004   Code   RO         5205    i.SPI_I2S_SendData  stm32f4xx_spi.o
    0x080059ac   0x080059ac   0x0000003c   Code   RO         5206    i.SPI_Init          stm32f4xx_spi.o
    0x080059e8   0x080059e8   0x00000048   Code   RO         1835    i.SPI_ReadByte      drive_ads1256.o
    0x08005a30   0x08005a30   0x00000054   Code   RO         1836    i.SPI_WriteByte     drive_ads1256.o
    0x08005a84   0x08005a84   0x00000002   Code   RO         5575    i.SVC_Handler       stm32f4xx_it.o
    0x08005a86   0x08005a86   0x00000002   PAD
    0x08005a88   0x08005a88   0x00000040   Code   RO         5354    i.SYSCFG_EXTILineConfig  stm32f4xx_syscfg.o
    0x08005ac8   0x08005ac8   0x000000ec   Code   RO         5655    i.SetSysClock       system_stm32f4xx.o
    0x08005bb4   0x08005bb4   0x00000078   Code   RO          320    i.Set_SamplingFre   user_adc.o
    0x08005c2c   0x08005c2c   0x00000074   Code   RO         2021    i.Set_TriggerFre    user_dac.o
    0x08005ca0   0x08005ca0   0x0000009c   Code   RO          481    i.Show_Val          user.o
    0x08005d3c   0x08005d3c   0x0000001c   Code   RO          321    i.Start_ADC_Sampling  user_adc.o
    0x08005d58   0x08005d58   0x00000028   Code   RO         5147    i.SysTick_CLKSourceConfig  misc.o
    0x08005d80   0x08005d80   0x00000084   Code   RO          706    i.SysTick_Handler   os_cpu.o
    0x08005e04   0x08005e04   0x00000068   Code   RO         5657    i.SystemInit        system_stm32f4xx.o
    0x08005e6c   0x08005e6c   0x0000005c   Code   RO          707    i.System_init       os_cpu.o
    0x08005ec8   0x08005ec8   0x000001f0   Code   RO         2430    i.TFT_LCD_Init      tft_lcd.o
    0x080060b8   0x080060b8   0x00000018   Code   RO         4409    i.TIM_Cmd           stm32f4xx_tim.o
    0x080060d0   0x080060d0   0x00000012   Code   RO         4412    i.TIM_DMACmd        stm32f4xx_tim.o
    0x080060e2   0x080060e2   0x00000012   Code   RO         4467    i.TIM_SelectOutputTrigger  stm32f4xx_tim.o
    0x080060f4   0x080060f4   0x00000084   Code   RO         4481    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x08006178   0x08006178   0x00000018   Code   RO         4483    i.TIM_UpdateDisableConfig  stm32f4xx_tim.o
    0x08006190   0x08006190   0x000000dc   Code   RO          708    i.Task_Create       os_cpu.o
    0x0800626c   0x0800626c   0x00000004   Code   RO          709    i.Task_End          os_cpu.o
    0x08006270   0x08006270   0x00000088   Code   RO         1128    i.USART1_IRQHandler  usart.o
    0x080062f8   0x080062f8   0x00000028   Code   RO         1771    i.USART6_IRQHandler  drive_communication.o
    0x08006320   0x08006320   0x0000001e   Code   RO         4956    i.USART_ClearITPendingBit  stm32f4xx_usart.o
    0x0800633e   0x0800633e   0x00000018   Code   RO         4959    i.USART_Cmd         stm32f4xx_usart.o
    0x08006356   0x08006356   0x0000001a   Code   RO         4962    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x08006370   0x08006370   0x00000054   Code   RO         4963    i.USART_GetITStatus  stm32f4xx_usart.o
    0x080063c4   0x080063c4   0x000000d4   Code   RO         4966    i.USART_Init        stm32f4xx_usart.o
    0x08006498   0x08006498   0x0000000a   Code   RO         4973    i.USART_ReceiveData  stm32f4xx_usart.o
    0x080064a2   0x080064a2   0x00000008   Code   RO         4976    i.USART_SendData    stm32f4xx_usart.o
    0x080064aa   0x080064aa   0x00000004   Code   RO         5576    i.UsageFault_Handler  stm32f4xx_it.o
    0x080064ae   0x080064ae   0x00000002   PAD
    0x080064b0   0x080064b0   0x00000040   Code   RO          323    i.User_ADC_GPIO_Init  user_adc.o
    0x080064f0   0x080064f0   0x0000004c   Code   RO          324    i.User_ADC_Init     user_adc.o
    0x0800653c   0x0800653c   0x0000002e   Code   RO         2023    i.User_DAC_Configure  user_dac.o
    0x0800656a   0x0800656a   0x00000002   PAD
    0x0800656c   0x0800656c   0x00000070   Code   RO         2024    i.User_DAC_DMA_Init  user_dac.o
    0x080065dc   0x080065dc   0x00000040   Code   RO         2025    i.User_DAC_GPIO_Init  user_dac.o
    0x0800661c   0x0800661c   0x00000022   Code   RO         2026    i.User_DAC_Init     user_dac.o
    0x0800663e   0x0800663e   0x00000002   PAD
    0x08006640   0x08006640   0x00000064   Code   RO         2027    i.User_DAC_TIM_Init  user_dac.o
    0x080066a4   0x080066a4   0x000000b8   Code   RO         1506    i.User_SPI_SendData  user_spi.o
    0x0800675c   0x0800675c   0x00000060   Code   RO          490    i.User_main         user.o
    0x080067bc   0x080067bc   0x00000078   Code   RO         2645    i.W25Q64_Init       w25q64.o
    0x08006834   0x08006834   0x00000058   Code   RO         2647    i.W25Q64_Read       w25q64.o
    0x0800688c   0x0800688c   0x0000004c   Code   RO         2648    i.W25Q64_ReadID     w25q64.o
    0x080068d8   0x080068d8   0x00000030   Code   RO         6150    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08006908   0x08006908   0x000003c4   Code   RO         6168    i.__hardfp_log      m_wm.l(log.o)
    0x08006ccc   0x08006ccc   0x00000004   PAD
    0x08006cd0   0x08006cd0   0x00000130   Code   RO         6009    i.__hardfp_log10    m_wm.l(log10.o)
    0x08006e00   0x08006e00   0x00000c50   Code   RO         6021    i.__hardfp_pow      m_wm.l(pow.o)
    0x08007a50   0x08007a50   0x0000007a   Code   RO         6059    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08007aca   0x08007aca   0x000000f8   Code   RO         6182    i.__kernel_poly     m_wm.l(poly.o)
    0x08007bc2   0x08007bc2   0x00000006   PAD
    0x08007bc8   0x08007bc8   0x00000030   Code   RO         6130    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x08007bf8   0x08007bf8   0x00000014   Code   RO         6131    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x08007c0c   0x08007c0c   0x00000014   Code   RO         6132    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x08007c20   0x08007c20   0x00000020   Code   RO         6133    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08007c40   0x08007c40   0x00000020   Code   RO         6134    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08007c60   0x08007c60   0x00000020   Code   RO         6136    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08007c80   0x08007c80   0x0000000e   Code   RO         5907    i._is_digit         c_w.l(__printf_wp.o)
    0x08007c8e   0x08007c8e   0x00000006   Code   RO         1129    i._sys_exit         usart.o
    0x08007c94   0x08007c94   0x000000f0   Code   RO          491    i.circulate_Av      user.o
    0x08007d84   0x08007d84   0x0000003a   Code   RO         1772    i.crc_16            drive_communication.o
    0x08007dbe   0x08007dbe   0x00000002   PAD
    0x08007dc0   0x08007dc0   0x00000044   Code   RO         1099    i.delay_ms          delay.o
    0x08007e04   0x08007e04   0x00000060   Code   RO         1100    i.delay_us          delay.o
    0x08007e64   0x08007e64   0x00000018   Code   RO         6146    i.fabs              m_wm.l(fabs.o)
    0x08007e7c   0x08007e7c   0x00000044   Code   RO         2293    i.font_init         fontupd.o
    0x08007ec0   0x08007ec0   0x00000074   Code   RO          325    i.init_iir_filter   user_adc.o
    0x08007f34   0x08007f34   0x00000010   Code   RO         6170    i.log               m_wm.l(log.o)
    0x08007f44   0x08007f44   0x00000044   Code   RO          669    i.main              main.o
    0x08007f88   0x08007f88   0x0000003c   Code   RO          495    i.menu_show         user.o
    0x08007fc4   0x08007fc4   0x0000018c   Code   RO         1773    i.sendData          drive_communication.o
    0x08008150   0x08008150   0x00000154   Code   RO          496    i.set_sweep         user.o
    0x080082a4   0x080082a4   0x0000006e   Code   RO         6061    i.sqrt              m_wm.l(sqrt.o)
    0x08008312   0x08008312   0x00000002   PAD
    0x08008314   0x08008314   0x00000030   Code   RO         1774    i.usartSendData     drive_communication.o
    0x08008344   0x08008344   0x0000002c   Code   RO         6266    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008370   0x08008370   0x00000018   Code   RO         5931    x$fpl$basic         fz_wm.l(basic.o)
    0x08008388   0x08008388   0x00000062   Code   RO         5933    x$fpl$d2f           fz_wm.l(d2f.o)
    0x080083ea   0x080083ea   0x00000002   PAD
    0x080083ec   0x080083ec   0x00000150   Code   RO         5935    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x0800853c   0x0800853c   0x00000010   Code   RO         6268    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x0800854c   0x0800854c   0x00000018   Code   RO         6096    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08008564   0x08008564   0x000002b0   Code   RO         5942    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08008814   0x08008814   0x00000078   Code   RO         6098    x$fpl$deqf          fz_wm.l(deqf.o)
    0x0800888c   0x0800888c   0x0000002e   Code   RO         5958    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x080088ba   0x080088ba   0x00000026   Code   RO         5957    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x080088e0   0x080088e0   0x00000078   Code   RO         5963    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08008958   0x08008958   0x00000154   Code   RO         5965    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08008aac   0x08008aac   0x0000009c   Code   RO         6100    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08008b48   0x08008b48   0x0000000c   Code   RO         6102    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08008b54   0x08008b54   0x0000006c   Code   RO         5967    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08008bc0   0x08008bc0   0x00000016   Code   RO         5936    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08008bd6   0x08008bd6   0x00000002   PAD
    0x08008bd8   0x08008bd8   0x00000198   Code   RO         6104    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08008d70   0x08008d70   0x000001d4   Code   RO         5937    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08008f44   0x08008f44   0x00000056   Code   RO         5969    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08008f9a   0x08008f9a   0x0000008c   Code   RO         6106    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08009026   0x08009026   0x0000000a   Code   RO         6361    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08009030   0x08009030   0x0000000a   Code   RO         6108    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800903a   0x0800903a   0x00000004   Code   RO         5971    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800903e   0x0800903e   0x00000004   Code   RO         5973    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08009042   0x08009042   0x00000064   Code   RO         6342    x$fpl$retnan        fz_wm.l(retnan.o)
    0x080090a6   0x080090a6   0x0000005c   Code   RO         6110    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x08009102   0x08009102   0x00000030   Code   RO         6369    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x08009132   0x08009132   0x00000000   Code   RO         6112    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08009132   0x08009132   0x0000a30c   Data   RO         2278    .constdata          fonts.o
    0x0801343e   0x0801343e   0x00000011   Data   RO         5915    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0801344f   0x0801344f   0x00000001   PAD
    0x08013450   0x08013450   0x00000088   Data   RO         6024    .constdata          m_wm.l(pow.o)
    0x080134d8   0x080134d8   0x00000026   Data   RO         6085    .constdata          c_w.l(_printf_fp_hex.o)
    0x080134fe   0x080134fe   0x00000002   PAD
    0x08013500   0x08013500   0x00000038   Data   RO         6171    .constdata          m_wm.l(log.o)
    0x08013538   0x08013538   0x00000008   Data   RO         6184    .constdata          m_wm.l(qnan.o)
    0x08013540   0x08013540   0x00000094   Data   RO         6237    .constdata          c_w.l(bigflt0.o)
    0x080135d4   0x080135d4   0x00000020   Data   RO         6474    Region$$Table       anon$$obj.o
    0x080135f4   0x080135f4   0x0000001c   Data   RO         6265    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08013610, Size: 0x00014090, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08013610   0x00000001   Data   RW          270    .data               app_led.o
    0x20000001   0x08013611   0x00000007   PAD
    0x20000008   0x08013618   0x00000044   Data   RW          327    .data               user_adc.o
    0x2000004c   0x0801365c   0x00000026   Data   RW          498    .data               user.o
    0x20000072   0x08013682   0x00000002   PAD
    0x20000074   0x08013684   0x0000001e   Data   RW          711    .data               os_cpu.o
    0x20000092   0x080136a2   0x00000002   PAD
    0x20000094   0x080136a4   0x00000006   Data   RW         1133    .data               usart.o
    0x2000009a   0x080136aa   0x00000002   PAD
    0x2000009c   0x080136ac   0x00000005   Data   RW         1583    .data               drive_ps2.o
    0x200000a1   0x080136b1   0x00000003   PAD
    0x200000a4   0x080136b4   0x00000020   Data   RW         2279    .data               fonts.o
    0x200000c4   0x080136d4   0x00000008   Data   RW         2431    .data               tft_lcd.o
    0x200000cc   0x080136dc   0x00000002   Data   RW         2659    .data               w25q64.o
    0x200000ce   0x080136de   0x00000010   Data   RW         4112    .data               stm32f4xx_rcc.o
    0x200000de   0x080136ee   0x00000002   PAD
    0x200000e0   0x080136f0   0x00000014   Data   RW         5658    .data               system_stm32f4xx.o
    0x200000f4        -       0x000011d0   Zero   RW          326    .bss                user_adc.o
    0x200012c4        -       0x00000960   Zero   RW          497    .bss                user.o
    0x20001c24        -       0x000099a0   Zero   RW          670    .bss                main.o
    0x2000b5c4        -       0x00000480   Zero   RW          710    .bss                os_cpu.o
    0x2000ba44        -       0x000000c8   Zero   RW         1132    .bss                usart.o
    0x2000bb0c        -       0x0000000a   Zero   RW         1582    .bss                drive_ps2.o
    0x2000bb16   0x08013704   0x00000002   PAD
    0x2000bb18        -       0x000003f0   Zero   RW         1775    .bss                drive_communication.o
    0x2000bf08        -       0x00000100   Zero   RW         2029    .bss                user_dac.o
    0x2000c008        -       0x00000021   Zero   RW         2294    .bss                fontupd.o
    0x2000c029   0x08013704   0x00000003   PAD
    0x2000c02c        -       0x00000060   Zero   RW         6275    .bss                c_w.l(libspace.o)
    0x2000c08c   0x08013704   0x00000004   PAD
    0x2000c090        -       0x00000000   Zero   RW         5691    HEAP                startup_stm32f40_41xxx.o
    0x2000c090        -       0x00008000   Zero   RW         5690    STACK               startup_stm32f40_41xxx.o



  Load Region LR$$.ARM.__AT_0x10000000 (Base: 0x10000000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x10000000 (Exec base: 0x10000000, Load base: 0x10000000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x10000000        -       0x0000f000   Zero   RW          986    .ARM.__AT_0x10000000  os_malloc.o



  Load Region LR$$.ARM.__AT_0x1000F000 (Base: 0x1000f000, Size: 0x00000000, Max: 0x00000f00, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x1000F000 (Exec base: 0x1000f000, Load base: 0x1000f000, Size: 0x00000f00, Max: 0x00000f00, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x1000f000        -       0x00000f00   Zero   RW          987    .ARM.__AT_0x1000F000  os_malloc.o



  Load Region LR$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x00000000, Max: 0x000f0000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68000000 (Exec base: 0x68000000, Load base: 0x68000000, Size: 0x000f0000, Max: 0x000f0000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68000000        -       0x000f0000   Zero   RW          988    .ARM.__AT_0x68000000  os_malloc.o



  Load Region LR$$.ARM.__AT_0x680F0000 (Base: 0x680f0000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x680F0000 (Exec base: 0x680f0000, Load base: 0x680f0000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x680f0000        -       0x0000f000   Zero   RW          989    .ARM.__AT_0x680F0000  os_malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       208         16          0          1          0       1005   app_led.o
         0          0          0          0          0     343544   app_touch.o
         0          0          0          0          0        689   character.o
       124         20          0          0          0        272   core.o
       164         12          0          0          0       1282   delay.o
       874         36          0          0          0       4944   drive_ads1256.o
      1050         54          0          0       1008       5601   drive_communication.o
        80         10          0          0          0        515   drive_gpio.o
      1112         34          0          5         10       7611   drive_ps2.o
         0          0      41740         32          0       1350   fonts.o
        68          4          0          0         33       1209   fontupd.o
        88         24          0          0      39328       1467   main.o
       180         24          0          0          0       2101   misc.o
       844        132          0         30       1152     253718   os_cpu.o
         0          0          0          0    1109760       1666   os_malloc.o
      1396         72          0          0          0       9581   os_ui.o
       308         24          0          0          0       1717   spi.o
        60         22        392          0      32768        836   startup_stm32f40_41xxx.o
       382         24          0          0          0       5011   stm32f4xx_adc.o
       234         30          0          0          0       4684   stm32f4xx_dac.o
       564         40          0          0          0       4405   stm32f4xx_dma.o
       184         16          0          0          0       2128   stm32f4xx_exti.o
       282          6          0          0          0       1845   stm32f4xx_fsmc.o
       240          0          0          0          0       3559   stm32f4xx_gpio.o
        22          0          0          0          0       3075   stm32f4xx_it.o
       424         54          0         16          0       6414   stm32f4xx_rcc.o
       112          0          0          0          0       3526   stm32f4xx_spi.o
        64          4          0          0          0        652   stm32f4xx_syscfg.o
       216         28          0          0          0       3610   stm32f4xx_tim.o
       394          8          0          0          0       5306   stm32f4xx_usart.o
       340         32          0         20          0       1701   system_stm32f4xx.o
      1758         40          0          8          0      11269   tft_lcd.o
       142         14          0          6        200       2804   usart.o
      7168       1104          0         38       2400      11345   user.o
      1738        228          0         68       4560      12861   user_adc.o
       472         38          0          0        256       3959   user_dac.o
       256         30          0          0          0       1203   user_pga2310.o
       328         10          0          0          0       1376   user_spi.o
       284         26          0          2          0       2324   w25q64.o

    ----------------------------------------------------------------------
     22194       <USER>      <GROUP>        244    1191480     732165   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        34          0          0         18          5          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       314          0          0          0          0      16685   arm_biquad_cascade_df1_f32.o
        22          0          0          0          0        926   arm_biquad_cascade_df1_init_f32.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
       184         44          0          0          0        744   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       980         92         56          0          0        440   log.o
       304         44          0          0          0        200   log10.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
       232          0          0          0          0        296   sqrt.o

    ----------------------------------------------------------------------
     14584        <USER>        <GROUP>          0        100      26507   Library Totals
        30          4          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       336          0          0          0          0      17611   arm_cortexM4lf_math.lib
      5528        202        231          0         96       2888   c_w.l
      3518        252          0          0          0       3576   fz_wm.l
      5172        476        200          0          0       2432   m_wm.l

    ----------------------------------------------------------------------
     14584        <USER>        <GROUP>          0        100      26507   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     36778       3150      42598        244    1191580     735412   Grand Totals
     36778       3150      42598        244    1191580     735412   ELF Image Totals
     36778       3150      42598        244          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                79376 (  77.52kB)
    Total RW  Size (RW Data + ZI Data)           1191824 (1163.89kB)
    Total ROM Size (Code + RO Data + RW Data)      79620 (  77.75kB)

==============================================================================

