/* ****************************
 * Project description:
 *
 * DAC initialization file
 *
 * Author: ??????? -> 2019 Mao
 *
 * Creation Date: 2021/11/03 - 1
 * ****************************/
 
/* ***************************** Include & Define Part     	*****************************
 * ????????????????
 * */
#include "User_DAC.h"
 
#define BaseVol 3.3

/* ***************************** Variable definition Part   *****************************
 * ??????????
 * */
 
 uint16_t WaveData[DACDataLength] = {0};
 uint16_t WaveCount = 0;
 float coefficent = 1.0;//????DA????????????

/* ***************************** Initialization Part        *****************************
 * ???????????
 * */

void User_DAC_Init()
{
	// ?????DAC???GPIO??
	User_DAC_GPIO_Init( 0 );
	
	// ??????????TIM6
	User_DAC_TIM_Init();
	
	// ?????TIM6???§Ø?????
	//User_DAC_TIM_NVIC_Init();
	
	// DAC???????DMA?????
	User_DAC_DMA_Init(DACDataLength);
	
	// ????DAC
	User_DAC_Configure();
	
	// ?????????1.65V
	DAC_SetChannel1Data( DAC_Align_12b_R , 0x07ff ); 
}

// ????DAC
void User_DAC_Configure()
{
	DAC_InitTypeDef DAC_InitStruct;
	
	// ????DAC
	DAC_DeInit();
	
	// ???DAC???
	RCC_APB1PeriphClockCmd( RCC_APB1Periph_DAC , ENABLE );
	
	// ???????????DAC
	DAC_StructInit( &DAC_InitStruct );	
	DAC_Init( DAC_Channel_1 , &DAC_InitStruct );
	
	// ?????????DAC
//	DAC_InitStruct.DAC_Trigger = DAC_Trigger_T6_TRGO;											// ????? -> TIM6
//	DAC_InitStruct.DAC_WaveGeneration = DAC_WaveGeneration_None;					// ???¦Â??? -> ??
//	DAC_InitStruct.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;// ???????? -> ?¦Ë????
//	DAC_InitStruct.DAC_OutputBuffer = DAC_OutputBuffer_Enable;  					// ????????? -> ???
//	DAC_Init( DAC_Channel_1 , &DAC_InitStruct );
	
	// ????DAC1???DMA????
	DAC_DMACmd( DAC_Channel_1 , ENABLE );
	
	// ???DAC???1
	DAC_Cmd( DAC_Channel_1 , ENABLE );
}

// ?????DAC???GPIO??
void User_DAC_GPIO_Init( uint8_t ch_num )
{
	GPIO_InitTypeDef GPIO_InitStruct;
	
	RCC_AHB1PeriphClockCmd( RCC_AHB1Periph_GPIOA , ENABLE );
	
	if( ch_num == 1 )
		GPIO_InitStruct.GPIO_Pin = GPIO_Pin_4;
	else
		GPIO_InitStruct.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5;
	
	GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AN;
	GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
	GPIO_InitStruct.GPIO_Speed = GPIO_High_Speed;
	
	GPIO_Init( GPIOA , &GPIO_InitStruct );
}

// DAC???????DMA?????
void User_DAC_DMA_Init(uint32_t DataLength)
{
	DMA_InitTypeDef DMA_InitStruct;
	
	// ???DMA1???
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA1, ENABLE);
	
	// ??¦ËDMA1??????5
	DMA_DeInit( DMA1_Stream1 );																							// ???????????
	
	DMA_InitStruct.DMA_Channel = DMA_Channel_7;															// DMA??? -> ???7
	DMA_InitStruct.DMA_Memory0BaseAddr = (uint32_t)&WaveData;								// ????? -> ????????
	DMA_InitStruct.DMA_PeripheralBaseAddr = (uint32_t)&DAC ->DHR12R1 ;			// ???? -> DAC1???12¦Ë????????????????
	DMA_InitStruct.DMA_DIR = DMA_DIR_MemoryToPeripheral;										// ?????? -> ?????????
	DMA_InitStruct.DMA_BufferSize = DataLength;													// ?????§³ -> DAC???????
  	DMA_InitStruct.DMA_PeripheralInc = DMA_PeripheralInc_Disable;						// ??????????	-> ??
  	DMA_InitStruct.DMA_MemoryInc = DMA_MemoryInc_Enable;										// ????????? -> ??
  	DMA_InitStruct.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;// ??????§³ -> ????
  	DMA_InitStruct.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;				// ????????§³ -> ????
  	DMA_InitStruct.DMA_Mode = DMA_Mode_Circular;														// ?????? -> ?????
  	DMA_InitStruct.DMA_Priority = DMA_Priority_High;												// DMA????? -> ???????
  	DMA_InitStruct.DMA_FIFOMode = DMA_FIFOMode_Disable;         						// FIFO?? -> ???
  	DMA_InitStruct.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;							// FIFO??? -> ?????
    DMA_InitStruct.DMA_MemoryBurst = DMA_MemoryBurst_Single;								// ??????
  	DMA_InitStruct.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;				// ???????
  DMA_Init( DMA1_Stream1 , &DMA_InitStruct );
	
	// ???DMA1?§Ø???
	//DMA_ClearFlag( DMA1_Stream5 , DMA_FLAG_TCIF0 );  
	// ???????????????§Ø?
	//DMA_ITConfig( DMA1_Stream5 , DMA_IT_TC , ENABLE );  
	
	// ???DMA1
	DMA_Cmd(DMA1_Stream1, ENABLE);
}

// DAC?????????TIM6?????
void User_DAC_TIM_Init()
{
	TIM_TimeBaseInitTypeDef TIM_BaseInitStruct;
	
	// ???TIM6???
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM6, ENABLE);
	
	// ????TIM6
	TIM_BaseInitStruct.TIM_Prescaler = (1000 - 1);						// ???????
	TIM_BaseInitStruct.TIM_CounterMode = TIM_CounterMode_Up;	// ??????? -> ???????
	TIM_BaseInitStruct.TIM_Period = (21 - 1);									// ??????
	TIM_BaseInitStruct.TIM_ClockDivision = TIM_CKD_DIV1;			// ?????
	TIM_BaseInitStruct.TIM_RepetitionCounter = 0;							// ?????????(TIM1??TIM8)
	TIM_TimeBaseInit( TIM6 , &TIM_BaseInitStruct );
	
	// ????? -> ?????????????
	TIM_SelectOutputTrigger( TIM6 , TIM_TRGOSource_Update );
	
	// ?????????? -> ???(????????????)
	TIM_UpdateDisableConfig( TIM6 , DISABLE ); 
	
	TIM_DMACmd( TIM6 , TIM_DMA_Update , ENABLE );
	
	// ????TIM6?????????§Ø?
	//TIM_ITConfig( TIM6 , TIM_IT_Update , ENABLE );
	
	// ???TIM6
	TIM_Cmd( TIM6 , DISABLE );
	// ??????????????1kHz
	Set_TriggerFre( 32 * 1000 );
}	

// ????????TIM3???§Ø?????
void User_DAC_TIM_NVIC_Init()
{
	NVIC_InitTypeDef NVIC_InitStruct;
	
	// Configure TIM6 -> NVIC
	NVIC_InitStruct.NVIC_IRQChannel = TIM6_DAC_IRQn;
	NVIC_InitStruct.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStruct.NVIC_IRQChannelPreemptionPriority = 0;
	NVIC_InitStruct.NVIC_IRQChannelSubPriority = 0;
	
	NVIC_PriorityGroupConfig( NVIC_PriorityGroup_2 );
	NVIC_Init( &NVIC_InitStruct );
}

/* ***************************** Custom Function Part       *****************************
 * ????Žï????
 */

// ??????????? ??????1>???????( 0:???????1:??????2:????? )
void Set_WaveData( uint8_t sign ,uint32_t DataLength)
{
	uint8_t count;
	float vol;
	User_DAC_DMA_Init(DataLength);
	if( sign == 0 )
	{
		for(count = 0; count <= DataLength; count++)
		{
			// ???float???????????
			float angle = 2.0f * PI * ((float)count / DataLength);
			float vol = sinf(angle);  // ???sinf??????sin
			vol = 1.65 + vol * coefficent;  
			WaveData[count] = (uint16_t)(vol / BaseVol * 0x0fff + 0.5f);
		}
	}
	else if( sign == 1 )
	{
		// ???????????
		for( count = 0 ; count < DataLength / 2 ; count ++ )
			WaveData[count] = 0x0fff;
		for( count = DataLength / 2 ; count < DataLength ; count ++ )
			WaveData[count] = 0;
	}
	else if( sign == 2 )
	{
		// ?????????????
		WaveData[0] = 0;
		for( count = 1 ; count < DataLength /2 ; count ++ )
		{
			vol = (float)count/DataLength *2 *BaseVol;
			WaveData[count] = vol / BaseVol *0x0fff;
			WaveData[DataLength - count] = WaveData[count];
		}
		WaveData[DataLength /2] = 0x0fff;
	}
	
}
// ????TIM6?????? ??????1>??°Ü???? ?????????????
float Set_TriggerFre( float tf )
{
	uint32_t psc = 1;
	uint32_t arr = 1;
	
	TIM_Cmd(TIM6 , DISABLE);						// ???????
	
	do																	// ?????????????????PSC??ARR?
	{
		arr = 84000000 / (tf * psc);
		psc ++;
	}
	while( arr > 65535 );
	psc --;
	
	tf = (float)84000000 / (psc * arr); // ??PSC??ARR???????????
	
	TIM6 -> ARR = arr - 1;							// ????????????????????
	TIM6 -> PSC = psc - 1;							// ????????????????????
	
	return tf;
}

void DAC1_Vol_Set(uint16_t v_output)
{
//	double temp = vol*4096/3.3;
	//DAC_SetChannel1Data(DAC_Align_12b_R,vol);//12 ¦Ë???????????
	switch(v_output)
	{
		//0.1v
		case 1:
			coefficent = 0.5;
		break;

		case 2:
			coefficent = 0.7;
		break;
		//0.2v
		case 3:
			coefficent = 0.9;
		break;
		
		case 4:
			coefficent = 1.1;
		break;

		case 5:
			coefficent = 1.3;
		break;

		case 6:
			coefficent = 1.65;
		break;
		//0.3v
		case 7:
			coefficent = 0.9;
		break;
		case 8:
			coefficent = 0.95;
		break;
		case 9:
			coefficent = 1.1;
		break;
	}
}
/* ***************************** IRQHandler Part    	     	*****************************
 * ?§Ø???§Ü?????
 */

//void TIM6_DAC_IRQHandler()
//{
//	// ????§Ø???¦Ë
//	TIM_ClearITPendingBit(TIM6,TIM_IT_Update);
//	
//	DAC_SetDualChannelData( DAC_Align_12b_R , WaveData[WaveCount] , WaveData[WaveCount] ); 
//	
//	WaveCount ++;
//	WaveCount %= DACDataLength; 
//}
 
 
/* ***************************** 						END 	   	     	*****************************/






